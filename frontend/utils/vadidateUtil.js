
export function checkValidate (validations = []) {
    let currentErrors = {};
    let pass = true;
    validations.forEach(({ field, value, message }) => {
        const isValid = typeof value === 'boolean' ? value : !!value;
        if (!isValid) {
            currentErrors[field] = message;
            pass = false;
        }
    });
    if (!pass) {
        return currentErrors;
    }
    return null;
}