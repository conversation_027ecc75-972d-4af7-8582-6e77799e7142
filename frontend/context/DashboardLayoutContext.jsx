"use client";
import React, { createContext, useContext, useState } from "react";

const DashboardLayoutContext = createContext();

export function DashboardLayoutProvider({ children }) {
  // Các state dùng chung cho toàn layout
  const [title, setTitle] = useState("");
  const [keySearch, setKeySearch] = useState("");
  const [isSearch, setIsSearch] = useState(false);
  const [isDetail, setIsDetail] = useState(false);
  const [isTurnLive, setIsTurnLive] = useState(false);

  return (
    <DashboardLayoutContext.Provider value={{
      title, setTitle,
      keySearch, setKeySearch,
      isSearch, setIsSearch,
      isDetail, setIsDetail,
      isTurnLive, setIsTurnLive,
    }}>
      {children}
    </DashboardLayoutContext.Provider>
  );
}

export function useDashboardLayout() {
  return useContext(DashboardLayoutContext);
}