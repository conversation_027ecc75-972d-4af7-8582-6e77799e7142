"use client";

import DashboardLayout from "../../components/layouts/DashboardLayout";
import CountdownWidget from "../../components/dashboard/CountdownWidget";
import {useDashboardLayout} from "@/context/DashboardLayoutContext";
import {useEffect} from "react";

const targetDate = new Date("2025-07-01");
export default function Dashboard() {
    const {setTitle, keySearch, setKeySearch, setIsSearch, setIsDetail, setIsTurnLive} = useDashboardLayout();

    useEffect(() => {
        setTitle("Trang chủ");
        setIsSearch(false);
        setIsDetail(false);
        setIsTurnLive(false);
        setKeySearch(keySearch);
        return () => {
            setIsSearch(false);
            setIsTurnLive(false);
        }
    }, []);
    return (
        // <DashboardLayout title="Trang chủ">
        //
        // </DashboardLayout>
        <div className="flex flex-col items-center justify-center h-full min-h-[calc(100vh-160px)]">
            <div className="bg-[#FFFFFF]  p-8 max-w-md w-full">
                <CountdownWidget
                    targetDate={targetDate}
                    title="Thời gian khai giảng đếm ngược"
                    message="Trong lúc đợi... sao không cày view TikTok cho Ông Ba Dạy Hoá nhỉ? 🤗"
                    onComplete={() => console.log("Đếm ngược hoàn thành!")}
                />
            </div>
        </div>
    );
}
