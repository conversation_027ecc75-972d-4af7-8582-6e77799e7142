import { DashboardLayoutProvider } from "@/context/DashboardLayoutContext";
import DashboardLayout from "@/components/layouts/DashboardLayout";
import {Toaster} from "react-hot-toast";

export default function QuanLyLayout({ children }) {
  return (
      <>
          <Toaster position="top-center" reverseOrder={false}
                   toastOptions={
                    {
                        duration: 6000,
                        success: {
                            style: {
                                background: '#079455',
                                color: '#fff',
                            },
                            iconTheme: {
                                primary: '#fff',
                                secondary: '#079455',
                            },
                        },
                        error: {
                            style: {
                                background: '#1e293b',
                                color: '#fff',
                            },
                        }
                    }} />
          <DashboardLayoutProvider>
              <DashboardLayout>
                  {children}
              </DashboardLayout>
          </DashboardLayoutProvider>
      </>

  );
}
