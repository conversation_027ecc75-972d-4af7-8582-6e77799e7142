"use client";

import React from "react";
import { useRouter } from "next/navigation";

const PrivacyPolicyPage = () => {
  const router = useRouter();

  return (
    <div className="flex items-center justify-center min-h-screen mx-4 md:my-12 my-8">
      <div className="max-w-[792px]">
        <h1 className="md:text-5xl text-4xl font-semibold text-[#181D27] mb-16 text-center">
          Chính sách bảo mật thông tin
        </h1>
        <p className="mb-[18px] text-lg text-[#535862] font-semibold ">
          Tại sao chúng tôi cần Chính Sách Bảo Mật?
        </p>
        <p className="mb-8 text-lg text-[#535862] ">
          Chúng tôi hiểu rằng thông tin cá nhân của bạn rất quan trọng. Ch<PERSON>h
          sách bảo mật này được tạo ra để{" "}
          <span className="font-semibold">
            bảo vệ thông tin của bạn và xây dựng lòng tin
          </span>{" "}
          giữa bạn và <span className="font-semibold">Team Thầy Ba</span>. Chúng
          tôi cam kết sử dụng thông tin bạn cung cấp{" "}
          <span className="font-semibold">
            chỉ cho mục đích hỗ trợ việc học tập của bạn
          </span>{" "}
          một cách tốt nhất, chẳng hạn như tạo tài khoản học, gửi tài liệu, và
          hỗ trợ khi bạn cần.{" "}
          <span className="font-semibold">
            Chúng tôi tuyệt đối không chia sẻ thông tin của bạn với bất kỳ ai
            khác
          </span>{" "}
          ngoài những người có trách nhiệm trực tiếp trong việc vận hành khóa
          học này. Chúng tôi muốn bạn an tâm rằng{" "}
          <span className="font-semibold">
            thông tin của bạn được an toàn và bảo mật
          </span>{" "}
          khi tham gia khóa học của chúng tôi.
        </p>

        <h2 className="text-2xl font-semibold text-[#181D27] mb-4">
          1. Thông tin chúng tôi thu thập:
        </h2>
        <ul className="list-disc ml-6 mb-8 text-left text-lg text-[#535862] font-normal">
          <li>Tên và họ</li>
          <li>Ngày tháng năm sinh</li>
          <li>Giới tính</li>
          <li>Thông tin liên hệ</li>
          <li>Địa chỉ cứ trú</li>
          <li>
            Thông tin về quá trình học tập và tiến độ (ví dụ: điểm số bài kiểm
            tra, tiến độ hoàn thành khoá học)
          </li>
          <li>
            Thông tin kĩ thuật (ví dụ: địa chỉ IP, loại trình duyệt, dữ liệu
            cookies - nếu website sử dụng)
          </li>
        </ul>

        <h2 className="text-2xl font-semibold text-[#181D27] mb-4 ">
          2. Mục đích thu thập và sử dụng thông tin:
        </h2>
        <ul className="list-disc ml-6 mb-8 text-left text-lg text-[#535862] font-normal">
          <li>Xác nhận danh tính học viên khi đăng ký và đăng nhập.</li>
          <li>
            Tạo tài khoản cá nhân để quản lý tiến độ và truy cập tài liệu.
          </li>
          <li>
            Cung cấp tài liệu học tập, bài giảng và thông báo liên quan đến khoá
            học.
          </li>
          <li>
            Liên hệ và hỗ trợ học viên khi có thắc mắc hoặc sự cố kĩ thuật.
          </li>
          <li>
            Gửi thông báo quan trọng về khoá học (lịch học, cập nhật nội dung ,
            thông báo đặc biệt).
          </li>
          <li>
            Cải thiện chất lượng khoá học và trải nghiệm người dùng (dựa trên
            phân tích dữ liệu ẩn danh và tổng hợp).
          </li>
          <li>Quản lý và vận hành khoá học một cách hiệu quả.</li>
        </ul>

        <h2 className="text-2xl font-semibold text-[#181D27] mb-4 ">
          3. Cam kết bảo mật thông tin:
        </h2>
        <ul className="list-disc ml-6 mb-8 text-left text-lg text-[#535862] font-normal">
          <li>
            <span className="font-semibold">
              Không chia sẻ thông tin cá nhân{" "}
            </span>
            của học viên với bất kỳ bên thứ ba nào{" "}
            <span className="font-semibold">
              cho mục đích thương mại hoặc tiếp thị
            </span>{" "}
            khi chưa có sự đồng ý rõ ràng từ học viên. Chúng tôi có thể chia sẻ
            thông tin với các nhà cung cấp dịch vụ bên thứ ba hợp tác với chúng
            tôi (ví dụ: nền tảng thanh toán, dịch vụ email marketing) nhưng chỉ
            trong phạm vi cần thiết để vận hành khóa học và tuân thủ các thỏa
            thuận bảo mật nghiêm ngặt.
          </li>
          <li>
            <span className="font-semibold">Bảo mật thông tin</span> bằng các
            biện pháp kỹ thuật và tổ chức phù hợp để ngăn chặn truy cập, sử
            dụng, tiết lộ, thay đổi hoặc phá hủy trái phép. Chúng tôi sử dụng
            các biện pháp bảo mật tiêu chuẩn ngành để bảo vệ dữ liệu của bạn.
          </li>
          <li>
            <span className="font-semibold">Chỉ nhân viên được uỷ quyền</span>{" "}
            và có cam kết bảo mật mới có quyền truy cập thông tin cá nhân của
            học viên, và chỉ trong phạm vi cần thiết để thực hiện nhiệm vụ được
            giao.
          </li>
          <li>
            <span className="font-semibold">Thời gian lưu trữ dữ liệu:</span> Dữ
            liệu cá nhân của bạn sẽ được lưu trữ trong vòng{" "}
            <span className="font-semibold">12 tháng</span> sau khi bạn hoàn
            thành khoá học hoặc kết thúc thời hạn truy cập khoá học. Sau thời
            gian này, chúng tôi tiến hành{" "}
            <span className="font-semibold">xóa dữ liệu một cách an toàn</span>{" "}
            theo quy định của chúng tôi.
          </li>
          <li>
            <span className="font-semibold">
              Cookies và Công nghệ theo dõi:
            </span>{" "}
            Website của chúng tôi có thể sử dụng cookies để (ghi nhớ thông tin
            đăng nhập của bạn, phân tích cách bạn sử dụng website để cải thiện
            trải nghiệm). Bạn có thể quản lý cài đặt cookies trong trình duyệt
            của mình. Chúng tôi cam kết sử dụng cookies một cách có trách nhiệm
            và minh bạch.
          </li>
          <li>
            <span className="font-semibold">Tuân thủ pháp luật:</span> Chúng tôi
            cam kết tuân thủ các quy định pháp luật hiện hành của Việt Nam về
            bảo vệ dữ liệu cá nhân.
          </li>
        </ul>

        <h2 className="text-2xl font-semibold text-[#181D27] mb-4 ">
          4. Quyền của bạn đối với thông tin cá nhân:
        </h2>
        <ul className="list-disc ml-6 mb-8 text-left text-lg text-[#535862] font-normal">
          <li>
            <span className="font-semibold">Quyền truy cập:</span> Bạn có quyền
            yêu cầu truy cập và xem thông tin cá nhân của mình mà chúng tôi đang
            lưu trữ.
          </li>
          <li>
            <span className="font-semibold">Quyền chỉnh sửa:</span> Bạn có quyền
            yêu cầu cập nhật, chỉnh sửa thông tin cá nhân nếu phát hiện thông
            tin không chính xác hoặc không đầy đủ.
          </li>
          <li>
            <span className="font-semibold">Quyền xoá bỏ:</span> Bạn có quyền
            yêu cầu xóa thông tin cá nhân của mình sau khi kết thúc khóa học,
            trừ khi việc lưu giữ thông tin là bắt buộc theo quy định pháp luật
            hoặc cho mục đích quản lý nội bộ hợp pháp.
          </li>
          <li>
            <span className="font-semibold">Quyền phản đối:</span> Bạn có quyền
            phản đối việc xử lý thông tin cá nhân của mình cho một số mục đích
            nhất định (ví dụ: nhận email marketing).
          </li>
          <li>
            <span className="font-semibold">Quyền khiếu nại:</span> Bạn có quyền
            liên hệ với chúng tôi để khiếu nại hoặc thắc mắc về việc xử lý thông
            tin cá nhân của bạn.
          </li>
        </ul>

        <h2 className="text-2xl font-semibold text-[#181D27] mb-4">
          5. Liên hệ:
        </h2>
        <p className="mb-[18px] font-normal  text-lg text-[#535862] ">
          Nếu bạn có câu hỏi nào về Chính sách Bảo mật này hoặc cách chúng tôi
          xử lý thông tin cá nhân của bạn, vui lòng liên hệ với chúng tôi qua:
        </p>
        <ul className="list-disc ml-6 text-left text-lg text-[#535862] font-normal">
          <li>
            <span className="font-semibold">Email:</span>{" "}
            <span className="underline"><EMAIL></span>
          </li>
          <li>
            <span className="font-semibold">Số điện thoại:</span> 0828949479
          </li>
        </ul>
      </div>
    </div>
  );
};

export default PrivacyPolicyPage;
