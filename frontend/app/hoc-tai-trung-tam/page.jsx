"use client";
import React, { useState, useEffect } from "react";
import Button from "../../components/Button";
import TextField from "../../components/TextField";
import YearSelect from "../../components/YearSelect";
import SchoolSelect from "../../components/SchoolSelect";
import LocationModal from "../../components/LocationModal";
import Notification from "../../components/Notification";
import strapi from "../api/strapi";
import { useNotification } from "../../context/NotificationContext";

const HocTaiTrungTamPage = () => {
  const { showNotification } = useNotification();
  const [selectedClass, setSelectedClass] = useState("");
  const [selectedSchedule, setSelectedSchedule] = useState(null);
  const [showLocationModal, setShowLocationModal] = useState(true);
  const [loading, setLoading] = useState(false);
  const [classSchedules, setClassSchedules] = useState({
    "Lớp 10": [],
    "Lớp 11": [],
    "Lớp 12": [],
  });

  const [formData, setFormData] = useState({
    name: "",
    yearofbirth: "",
    phone: "",
    email: "",
    address: "",
    grade: "",
    classname: "",
    school: "",
  });
  const [formErrors, setFormErrors] = useState({});

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchClassSchedules = async () => {
      try {
        setLoading(true);
        const response = await strapi.classSchedules.getAll();
        console.log("response", response.data);

        const schedulesByGrade = {
          "Lớp 10": [],
          "Lớp 11": [],
          "Lớp 12": [],
        };

        const data = response.data;
        if (data && Array.isArray(data)) {
          // Chỉ lấy lịch offline
          const offlineData = data.filter((item) => (item.type || item.attributes?.type) === 'offline');
          offlineData.forEach((item) => {
            if (item && item.grade && schedulesByGrade[item.grade]) {
              let scheduleItems = [];

              if (item.schedule) {
                if (Array.isArray(item.schedule)) {
                  scheduleItems = item.schedule;
                } else if (typeof item.schedule === "string") {
                  scheduleItems = [item.schedule];
                }
              }

              schedulesByGrade[item.grade].push({
                id: item.id,
                name: item.name,
                date: item.date
                  ? new Date(item.date).toLocaleDateString("vi-VN")
                  : "Sẽ thông báo qua Email",
                schedule: scheduleItems,
              });
            }
          });
        }

        setClassSchedules(schedulesByGrade);
      } catch (error) {
        console.error("Lỗi khi lấy dữ liệu lịch học:", error);
        setError("Có lỗi xảy ra khi tải dữ liệu lịch học");
      } finally {
        setLoading(false);
      }
    };

    fetchClassSchedules();
  }, []);

  // Hàm xử lý thay đổi input với validation
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));

    // Cập nhật errors
    let newErrors = { ...formErrors };

    switch (name) {
      case "name":
        newErrors.name = !value ? "Vui lòng nhập họ tên" : null;
        break;
      case "phone":
        newErrors.phone = !value
          ? "Vui lòng nhập số điện thoại"
          : !/^[0-9]{10}$/.test(value)
          ? "Số điện thoại không hợp lệ (cần 10 số)"
          : null;
        break;
      case "email":
        newErrors.email = !value
          ? "Vui lòng nhập email"
          : !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)
          ? "Email không hợp lệ"
          : null;
        break;
      case "address":
        newErrors.address = !value ? "Vui lòng nhập địa chỉ" : null;
        break;
      default:
        break;
    }

    setFormErrors(newErrors);
  };

  // Hàm xử lý clear input
  const handleClearInput = (fieldName) => {
    setFormData((prev) => ({
      ...prev,
      [fieldName]: "",
    }));
  };

  // Thêm hàm validate chi tiết
  const validateFormData = (data) => {
    const errors = [];

    // Kiểm tra name
    if (!data.name) errors.push("Vui lòng nhập họ tên");

    // Kiểm tra yearofbirth
    if (!data.yearofbirth) errors.push("Vui lòng chọn năm sinh");

    // Kiểm tra phone
    if (!data.phone) {
      errors.push("Vui lòng nhập số điện thoại");
    } else if (!/^[0-9]{10}$/.test(data.phone)) {
      errors.push("Số điện thoại không hợp lệ (cần 10 số)");
    }

    // Kiểm tra email
    if (!data.email) {
      errors.push("Vui lòng nhập email");
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
      errors.push("Email không hợp lệ");
    }

    // Kiểm tra address
    if (!data.address) errors.push("Vui lòng nhập địa chỉ");

    // Kiểm tra grade
    if (!data.grade) errors.push("Vui lòng chọn khối lớp");

    // Kiểm tra classname
    if (!data.classname) errors.push("Vui lòng chọn lớp học");

    // Kiểm tra school
    if (!data.school) errors.push("Vui lòng chọn trường học");

    return errors;
  };

  // Hàm xử lý submit form
  const handleSubmit = async () => {
    try {
      setIsSubmitting(true);
      setError(null);

      // Validate chi tiết
      const validationErrors = validateFormData(formData);
      if (validationErrors.length > 0) {
        throw new Error(validationErrors.join("\n"));
      }

      // Chuẩn bị data gửi lên API
      const submitData = {
        data: {
          name: formData.name.trim(),
          yearofbirth: formData.yearofbirth,
          phone: formData.phone.trim(),
          email: formData.email.trim(),
          address: formData.address.trim(),
          grade: formData.grade.replace("Lớp ", "").trim(),
          classname: formData.classname.replace("Lớp ", "").trim(),
          school: formData.school,
          class_schedules:
            selectedSchedule !== null
              ? [classSchedules[selectedClass][selectedSchedule].id]
              : [],
        },
      };

      // Gọi API
      try {
        const response = await strapi.offlineForm.send(submitData);

        // Hiển thị notification thành công
        showNotification({
          type: "success",
          title: "Đăng ký thành công",
          message: `Bạn đã đăng ký học tại Trung tâm thành công. Chúng tôi sẽ sớm thông báo đến bạn thông qua email "${formData.email}"`,
          actionText: "Quay về Trang chủ",
          onAction: () => {
            window.location.href = "/";
          },
        });

        // Reset form
        setFormData({
          name: "",
          yearofbirth: "",
          phone: "",
          email: "",
          address: "",
          grade: "",
          classname: "",
          school: "",
        });
        setSelectedClass("");
        setSelectedSchedule(null);
      } catch (apiError) {
        console.error("API Error:", apiError);
        console.error("Error response:", apiError.response?.data);
        showNotification({
          type: "error",
          title: "Lỗi",
          message:
            apiError.response?.data?.error?.message ||
            "Có lỗi xảy ra khi gửi form",
          duration: 5000,
        });
      }
    } catch (err) {
      setError(err.message || "Có lỗi xảy ra, vui lòng thử lại");
      showNotification({
        type: "error",
        title: "Lỗi",
        message: err.message || "Có lỗi xảy ra, vui lòng thử lại",
        duration: 5000,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      {showLocationModal && (
        <LocationModal onClose={() => setShowLocationModal(false)} />
      )}
      <div className="flex flex-col justify-center items-center mx-auto py-12 px-4 bg-[#FFFFFF]">
        <div className="flex flex-col sm:max-w-[590px] gap-8 ">
          <div className="flex flex-col justify-center items-center gap-3">
            <h1 className="text-3xl leading-[38px] font-semibold text-center sm:w-[510px]">
              Đăng ký học tại Trung tâm khoá 2025 - 2026
            </h1>
            <p className="text-base text-center font-normal">
              Để hoàn tất đăng ký, các em vui lòng điền đầy đủ thông tin chi tiết và chọn lớp học phù hợp dưới đây.
            </p>
          </div>

          <div className="p-6 border border-[#E9EAEB] rounded-2xl">
            <h2 className="text-lg font-semibold">Thông tin cá nhân</h2>
            <div className="flex flex-col gap-[20px] mt-5">
              <div className="flex flex-col sm:flex-row gap-[20px]">
                <div className="sm:w-[353px]">
                  <TextField
                    label="Họ và tên"
                    name="name"
                    id="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    placeholder="Nhập họ và tên"
                    required
                    error={formErrors.name}
                    onClear={() => handleClearInput("name")}
                  />
                </div>
                <div className="sm:w-[169px]">
                  <label htmlFor="yearofbirth" className="text-sm font-medium ">
                    Năm sinh <span className="text-[#D92D20]">*</span>
                  </label>
                  <div className="mt-[6px]">
                    <YearSelect
                      onChange={(year) =>
                        setFormData((prev) => ({
                          ...prev,
                          yearofbirth: year.toString(),
                        }))
                      }
                    />
                  </div>
                </div>
              </div>

              <TextField
                label="Số điện thoại"
                name="phone"
                id="phone"
                value={formData.phone}
                onChange={handleInputChange}
                placeholder="Nhập số điện thoại"
                required
                error={formErrors.phone}
                onClear={() => handleClearInput("phone")}
              />

              <TextField
                label="Email"
                name="email"
                id="email"
                value={formData.email}
                onChange={handleInputChange}
                placeholder="Nhập email"
                required
                error={formErrors.email}
                onClear={() => handleClearInput("email")}
              />

              <TextField
                label="Địa chỉ nhà"
                name="address"
                id="address"
                value={formData.address}
                onChange={handleInputChange}
                placeholder="Nhập địa chỉ"
                required
                error={formErrors.address}
                onClear={() => handleClearInput("address")}
              />

              <div className="w-full">
                <label htmlFor="school" className="text-sm font-medium">
                  Trường học <span className="text-[#D92D20]">*</span>
                </label>
                <div className="mt-[6px]">
                  <SchoolSelect
                    onChange={(school) =>
                      setFormData((prev) => ({ ...prev, school: school }))
                    }
                  />
                </div>
              </div>
            </div>
          </div>

          <div className="p-6 border border-[#E9EAEB] rounded-2xl w-full">
            <h2 className="text-lg font-semibold">Thông tin lớp học</h2>
            <div className="flex flex-col gap-[20px]">
              <div className="w-full">
                <label htmlFor="khoahoc" className="text-sm font-medium">
                  Khối lớp <span className="text-[#D92D20]">*</span>
                </label>
                <div className="mt-[6px] flex flex-col sm:flex-row gap-4 w-full">
                  <button
                    className={`w-full py-2.5 px-4 text-base font-medium rounded-lg focus:outline-none flex items-center justify-center gap-2 ${
                      selectedClass === "Lớp 10"
                        ? "border-2 border-[#299D55]"
                        : "border-2 border-[#D5D7DA]"
                    }`}
                    onClick={() => {
                      setSelectedClass("Lớp 10");
                      setFormData((prev) => ({ ...prev, grade: "Lớp 10" }));
                    }}
                  >
                    {selectedClass === "Lớp 10" && (
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="21"
                        height="20"
                        viewBox="0 0 21 20"
                        fill="none"
                      >
                        <path
                          d="M17.1668 5L8.00016 14.1667L3.8335 10"
                          stroke="#299D55"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </svg>
                    )}
                    <span>Lớp 10</span>
                  </button>
                  <button
                    className={`w-full py-2.5 px-4 text-base font-medium rounded-lg focus:outline-none flex items-center justify-center gap-2 ${
                      selectedClass === "Lớp 11"
                        ? "border-2 border-[#299D55] "
                        : "border-2 border-[#D5D7DA] "
                    }`}
                    onClick={() => {
                      setSelectedClass("Lớp 11");
                      setFormData((prev) => ({ ...prev, grade: "Lớp 11" }));
                    }}
                  >
                    {selectedClass === "Lớp 11" && (
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="21"
                        height="20"
                        viewBox="0 0 21 20"
                        fill="none"
                      >
                        <path
                          d="M17.1668 5L8.00016 14.1667L3.8335 10"
                          stroke="#299D55"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </svg>
                    )}
                    <span>Lớp 11</span>
                  </button>
                  <button
                    className={`w-full py-2.5 px-4 text-base font-medium rounded-lg focus:outline-none flex items-center justify-center gap-2 ${
                      selectedClass === "Lớp 12"
                        ? "border-2 border-[#299D55] "
                        : "border-2 border-[#D5D7DA] "
                    }`}
                    onClick={() => {
                      setSelectedClass("Lớp 12");
                      setFormData((prev) => ({ ...prev, grade: "Lớp 12" }));
                    }}
                  >
                    {selectedClass === "Lớp 12" && (
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="21"
                        height="20"
                        viewBox="0 0 21 20"
                        fill="none"
                      >
                        <path
                          d="M17.1668 5L8.00016 14.1667L3.8335 10"
                          stroke="#299D55"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </svg>
                    )}
                    <span>Lớp 12</span>
                  </button>
                </div>
              </div>
            </div>
            {selectedClass && loading ? (
              <div className="mt-4 flex justify-center">
                <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-[#299D55]"></div>
              </div>
            ) : selectedClass && classSchedules[selectedClass].length > 0 ? (
              <div className="mt-4">
                <label htmlFor="lichhoc" className="text-sm font-medium">
                  Lịch học <span className="text-[#D92D20]">*</span>
                </label>
                <div className="grid grid-cols-1 gap-4 mt-1">
                  {classSchedules[selectedClass].map((schedule, index) => (
                    <div
                      key={index}
                      onClick={() => {
                        setSelectedSchedule(index);
                        setFormData((prev) => ({
                          ...prev,
                          classname: schedule.name,
                        }));
                      }}
                      className={`border-2 rounded-2xl p-6 cursor-pointer transition-all duration-200 ${
                        selectedSchedule === index
                          ? "border-[#299D55] ring-2 ring-[#E6F5EB]"
                          : "border-[#E9EAEB] hover:border-[#299D55]"
                      }`}
                    >
                      <div className="flex justify-between gap-1">
                        <div className="flex flex-col gap-1">
                          <h4 className="text-xl leading-[30px] font-semibold text-[#181D27]">
                            {schedule.name}
                          </h4>
                          <p className="text-sm font-normal text-[#717680]">
                            Ngày khai giảng: {schedule.date}
                          </p>
                        </div>
                        <div
                          className={`w-[20px] h-[20px] border-2 rounded-full transition-all duration-200 ${
                            selectedSchedule === index
                              ? "border-[#299D55] border-[6px]"
                              : "border-[#D5D7DA]"
                          }`}
                        ></div>
                      </div>

                      <div className="flex flex-col gap-1 mt-4">
                        {schedule.schedule.map((time, idx) => (
                          <div key={idx} className="flex gap-1 items-center">
                            {idx === 0 && (
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="20"
                                height="20"
                                viewBox="0 0 20 20"
                                fill="none"
                              >
                                <path
                                  d="M17.5 8.33366H2.5M13.3333 1.66699V5.00033M6.66667 1.66699V5.00033M6.5 18.3337H13.5C14.9001 18.3337 15.6002 18.3337 16.135 18.0612C16.6054 17.8215 16.9878 17.439 17.2275 16.9686C17.5 16.4339 17.5 15.7338 17.5 14.3337V7.33366C17.5 5.93353 17.5 5.23346 17.2275 4.69868C16.9878 4.22828 16.6054 3.84583 16.135 3.60614C15.6002 3.33366 14.9001 3.33366 13.5 3.33366H6.5C5.09987 3.33366 4.3998 3.33366 3.86502 3.60614C3.39462 3.84583 3.01217 4.22828 2.77248 4.69868C2.5 5.23346 2.5 5.93353 2.5 7.33366V14.3337C2.5 15.7338 2.5 16.4339 2.77248 16.9686C3.01217 17.439 3.39462 17.8215 3.86502 18.0612C4.3998 18.3337 5.09987 18.3337 6.5 18.3337Z"
                                  stroke="#717680"
                                  strokeWidth="1.33333"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                />
                              </svg>
                            )}
                            <p
                              className={`text-base text-[#667085] ${
                                idx !== 0 ? "ml-6" : ""
                              }`}
                            >
                              {time}
                            </p>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ) : selectedClass && classSchedules[selectedClass].length === 0 ? (
              <div className="mt-4 text-center text-sm text-[#D92D20]">
                Không có lịch học nào cho khối lớp này.
              </div>
            ) : null}
          </div>
          <div className="flex flex-col gap-1">
            <div className="max-sm:text-center text-base text-[#535862] font-semibold">
              Trung tâm thầy Ba dạy Hoá
            </div>
            <div className="max-sm:justify-center text-base text-[#535862] font-normal flex gap-2">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 20 20"
                fill="none"
              >
                <path
                  d="M9.99992 10.8333C11.3806 10.8333 12.4999 9.714 12.4999 8.33329C12.4999 6.95258 11.3806 5.83329 9.99992 5.83329C8.61921 5.83329 7.49992 6.95258 7.49992 8.33329C7.49992 9.714 8.61921 10.8333 9.99992 10.8333Z"
                  stroke="#535862"
                  strokeWidth="1.66667"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <path
                  d="M9.99992 18.3333C13.3333 15 16.6666 12.0152 16.6666 8.33329C16.6666 4.65139 13.6818 1.66663 9.99992 1.66663C6.31802 1.66663 3.33325 4.65139 3.33325 8.33329C3.33325 12.0152 6.66659 15 9.99992 18.3333Z"
                  stroke="#535862"
                  strokeWidth="1.66667"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
              21 Đ. Chu Văn An, Tân An, Buôn Ma Thuột, Đắk Lắk, Việt Nam
            </div>
          </div>
          <Button
            variant="primary"
            disabled={isSubmitting}
            onClick={handleSubmit}
            className="w-full"
          >
            {isSubmitting ? "Đang xử lý..." : "Đăng ký"}
          </Button>
        </div>
      </div>
    </>
  );
};

export default HocTaiTrungTamPage;
