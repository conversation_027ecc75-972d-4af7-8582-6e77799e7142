"use client";

import { useState, useEffect, useRef } from "react";
import Button from "../components/Button";
import QuizGradeSelect from "../components/QuizGradeSelect";
import GradeSelect from "../components/GradeSelect";
import CtaSection from "../components/CtaSection";
import Radio from "../components/Radio";
import Image from "next/image";
import MarkerPinIcon from "../public/images/homepage/marker-pin-05.svg";
import TiktokIcon from "../public/images/homepage/TikTok.svg";
import LineChartIcon from "../public/images/homepage/line-chart-up-02.svg";
import ChalkboardIcon from "../public/images/homepage/Chalkboard_solid.svg";
import UserPlusIcon from "../public/images/homepage/user-plus-01.svg";
import ChevronIcon from "../public/images/homepage/chevron-right.svg";
import strapiApi from "./api/strapi";
import { event as gaEvent } from '../utils/analytics'; // Import hàm helper

export default function Home() {
  const [showQuizGradeSelect, setShowQuizGradeSelect] = useState(false);
  const [activeFaq, setActiveFaq] = useState(null);
  const [activeGrade, setActiveGrade] = useState("lop10");

  const [activeTestimonial, setActiveTestimonial] = useState(0);
  const [activeParentTestimonial, setActiveParentTestimonial] = useState(0);
  const [showGradeSelect, setShowGradeSelect] = useState(false);
  // State cho dữ liệu testimonials từ API
  const [studentTestimonials, setStudentTestimonials] = useState([]);
  const [parentTestimonials, setParentTestimonials] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedQuizOption, setSelectedQuizOption] = useState(null);

  // Thêm các state mới vào đầu component, sau các state có sẵn
  const [totalStudentPages, setTotalStudentPages] = useState(1);
  const [totalParentPages, setTotalParentPages] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(1);

  // Thêm state mới để quản lý swipe
  const [isDragging, setIsDragging] = useState(false);
  const [startX, setStartX] = useState(0);
  const [activeCarousel, setActiveCarousel] = useState(null); // student hoặc parent
  const [studentDragOffset, setStudentDragOffset] = useState(0);
  const [parentDragOffset, setParentDragOffset] = useState(0);
  const studentCarouselRef = useRef(null);
  const parentCarouselRef = useRef(null);

  // Thêm useEffect để tính toán số trang dựa trên kích thước màn hình và số lượng testimonial
  useEffect(() => {
    function calculatePages() {
      // Xác định số lượng testimonial trên mỗi slide dựa trên kích thước màn hình
      let itemsPerSlide = 1; // Mặc định cho mobile
      
      if (typeof window !== 'undefined') {
        const width = window.innerWidth;
        if (width >= 1024) { // lg breakpoint
          itemsPerSlide = 3;
        } else if (width >= 768) { // md breakpoint
          itemsPerSlide = 2;
        }
      }
      
      setItemsPerPage(itemsPerSlide);
      
      // Tính toán số trang cho học sinh và phụ huynh
      const studentPages = Math.ceil(studentTestimonials.length / itemsPerSlide);
      const parentPages = Math.ceil(parentTestimonials.length / itemsPerSlide);
      
      setTotalStudentPages(studentPages || 1);
      setTotalParentPages(parentPages || 1);
    }
    
    calculatePages();
    
    // Thêm event listener để tính lại số trang khi resize
    window.addEventListener('resize', calculatePages);
    
    return () => {
      window.removeEventListener('resize', calculatePages);
    };
  }, [studentTestimonials, parentTestimonials]);

  // Hàm cuộn đến phần tử có id tương ứng
  const scrollToSection = (id) => {
    let targetId;

    // Xử lý nhất quán giữa các nút
    if (id === "course-header") {
      targetId = "course-header";
    } else if (id === "course") {
      targetId = "course";
    } else if (id === "learning-method-header") {
      targetId = "learning-method-header";
    } else {
      targetId = id;
    }

    const element = document.getElementById(targetId);

    if (element) {
      let headerHeight;

      // Áp dụng offset phù hợp dựa trên ID gốc được truyền vào
      switch (id) {
        case "course-header":
          headerHeight = 150;
          break;
        case "course":
          headerHeight = 120;
          break;
        case "learning-method-header":
          headerHeight = 150;
          break;
        default:
          headerHeight = 120;
      }

      setTimeout(() => {
        const elementPosition = element.getBoundingClientRect().top;
        const offsetPosition =
          elementPosition + window.pageYOffset - headerHeight;

        window.scrollTo({
          top: offsetPosition,
          behavior: "smooth",
        });
      }, 80);
    }
  };

  const handleSelectGrade = (gradeId) => {
    setShowGradeSelect(false);
  };
  const handleQuizGradeSelect = () => {
    setShowQuizGradeSelect(false);
  };

  const handleGradeChange = (grade) => {
    setActiveGrade(grade);
  };

  const toggleFaq = (index) => {
    setActiveFaq(activeFaq === index ? null : index);
  };



  const handleTestimonialChange = (direction) => {
    if (!studentTestimonials || studentTestimonials.length === 0) return;

    const currentPage = Math.floor(activeTestimonial / itemsPerPage);
    
    if (direction === "next") {
      const nextPage = (currentPage + 1) % totalStudentPages;
      setActiveTestimonial(nextPage * itemsPerPage);
    } else {
      const prevPage = (currentPage === 0) ? totalStudentPages - 1 : currentPage - 1;
      setActiveTestimonial(prevPage * itemsPerPage);
    }
  };

  const handleParentTestimonialChange = (direction) => {
    if (!parentTestimonials || parentTestimonials.length === 0) return;

    const currentPage = Math.floor(activeParentTestimonial / itemsPerPage);
    
    if (direction === "next") {
      const nextPage = (currentPage + 1) % totalParentPages;
      setActiveParentTestimonial(nextPage * itemsPerPage);
    } else {
      const prevPage = (currentPage === 0) ? totalParentPages - 1 : currentPage - 1;
      setActiveParentTestimonial(prevPage * itemsPerPage);
    }
  };

  // Fetch testimonials từ API
  useEffect(() => {
    async function fetchTestimonials() {
      try {
        setIsLoading(true);

        // Lấy tất cả testimonials
        const allTestimonials =
          await strapiApi.testimonials.getAllTestimonials();

        // console.log("Testimonials API Response:", allTestimonials); // commented out to prevent sensitive data in console

        if (allTestimonials && allTestimonials.data) {
          // Phân loại testimonials theo role
          const studentData = allTestimonials.data.filter(
            (item) => item.role === "student"
          );
          const parentData = allTestimonials.data.filter(
            (item) => item.role === "parent"
          );

          // console.log("Student Testimonial Sample:", studentData[0]); // commented out to prevent console noise
          // console.log("Avatar field:", studentData[0]?.avatar); // commented out to prevent console noise
          
          // console.log("Full Image URL:", fullImageUrl); // commented out to prevent console noise
          
          setStudentTestimonials(studentData || []);
          setParentTestimonials(parentData || []);
        }
      } catch (error) {
        console.error("Error fetching testimonials:", error);
      } finally {
        setIsLoading(false);
      }
    }

    fetchTestimonials();
  }, []);

  // Hàm xử lý sự kiện chuột/touch cho carousel
  const handleMouseDown = (e, carouselRef, isParent = false) => {
    setIsDragging(true);
    setStartX(e.pageX - carouselRef.current.offsetLeft);
    setActiveCarousel(isParent ? 'parent' : 'student');
    
    if (isParent) {
      setParentDragOffset(0);
    } else {
      setStudentDragOffset(0);
    }
  };

  const handleTouchStart = (e, carouselRef, isParent = false) => {
    setIsDragging(true);
    setStartX(e.touches[0].pageX - carouselRef.current.offsetLeft);
    setActiveCarousel(isParent ? 'parent' : 'student');
    
    if (isParent) {
      setParentDragOffset(0);
    } else {
      setStudentDragOffset(0);
    }
  };

  const handleMouseMove = (e, carouselRef, isParent = false) => {
    if (!isDragging || activeCarousel !== (isParent ? 'parent' : 'student')) return;
    e.preventDefault();
    const x = e.pageX - carouselRef.current.offsetLeft;
    const walk = (x - startX);
    
    // Kiểm tra và giới hạn kéo/vuốt
    if (isParent) {
      // Xác định giới hạn kéo cho parent carousel
      const isFirstSlide = activeParentTestimonial === 0;
      const isLastSlide = activeParentTestimonial >= Math.floor((parentTestimonials.length - 1) / itemsPerPage) * itemsPerPage;
      
      // Nếu là slide đầu, không cho kéo sang phải (walk > 0)
      // Nếu là slide cuối, không cho kéo sang trái (walk < 0)
      if ((isFirstSlide && walk > 0) || (isLastSlide && walk < 0)) {
        // Cho phép kéo với lực cản (resistance) - chỉ cho phép kéo 1/4 khoảng cách
        setParentDragOffset(walk / 4);
      } else {
        setParentDragOffset(walk);
      }
    } else {
      // Xác định giới hạn kéo cho student carousel
      const isFirstSlide = activeTestimonial === 0;
      const isLastSlide = activeTestimonial >= Math.floor((studentTestimonials.length - 1) / itemsPerPage) * itemsPerPage;
      
      // Nếu là slide đầu, không cho kéo sang phải (walk > 0)
      // Nếu là slide cuối, không cho kéo sang trái (walk < 0)
      if ((isFirstSlide && walk > 0) || (isLastSlide && walk < 0)) {
        // Cho phép kéo với lực cản (resistance) - chỉ cho phép kéo 1/4 khoảng cách
        setStudentDragOffset(walk / 4);
      } else {
        setStudentDragOffset(walk);
      }
    }
  };

  const handleTouchMove = (e, carouselRef, isParent = false) => {
    if (!isDragging || activeCarousel !== (isParent ? 'parent' : 'student')) return;
    const x = e.touches[0].pageX - carouselRef.current.offsetLeft;
    const walk = (x - startX);
    
    // Kiểm tra và giới hạn kéo/vuốt
    if (isParent) {
      // Xác định giới hạn kéo cho parent carousel
      const isFirstSlide = activeParentTestimonial === 0;
      const isLastSlide = activeParentTestimonial >= Math.floor((parentTestimonials.length - 1) / itemsPerPage) * itemsPerPage;
      
      // Nếu là slide đầu, không cho kéo sang phải (walk > 0)
      // Nếu là slide cuối, không cho kéo sang trái (walk < 0)
      if ((isFirstSlide && walk > 0) || (isLastSlide && walk < 0)) {
        // Cho phép kéo với lực cản (resistance) - chỉ cho phép kéo 1/4 khoảng cách
        setParentDragOffset(walk / 4);
      } else {
        setParentDragOffset(walk);
      }
    } else {
      // Xác định giới hạn kéo cho student carousel
      const isFirstSlide = activeTestimonial === 0;
      const isLastSlide = activeTestimonial >= Math.floor((studentTestimonials.length - 1) / itemsPerPage) * itemsPerPage;
      
      // Nếu là slide đầu, không cho kéo sang phải (walk > 0)
      // Nếu là slide cuối, không cho kéo sang trái (walk < 0)
      if ((isFirstSlide && walk > 0) || (isLastSlide && walk < 0)) {
        // Cho phép kéo với lực cản (resistance) - chỉ cho phép kéo 1/4 khoảng cách
        setStudentDragOffset(walk / 4);
      } else {
        setStudentDragOffset(walk);
      }
    }
  };

  const handleMouseUp = (isParent = false) => {
    if (!isDragging || activeCarousel !== (isParent ? 'parent' : 'student')) return;
    
    // Xác định hướng kéo
    const dragOffset = isParent ? parentDragOffset : studentDragOffset;
    
    // Lấy thông tin về slide hiện tại
    const isFirstSlide = isParent 
      ? activeParentTestimonial === 0
      : activeTestimonial === 0;
    
    const isLastSlide = isParent
      ? activeParentTestimonial >= Math.floor((parentTestimonials.length - 1) / itemsPerPage) * itemsPerPage
      : activeTestimonial >= Math.floor((studentTestimonials.length - 1) / itemsPerPage) * itemsPerPage;
    
    // Chỉ cho phép chuyển slide nếu không phải là slide đầu/cuối hoặc đúng hướng
    if (dragOffset > 100 && !isFirstSlide) {
      // Kéo sang phải -> hiển thị slide trước đó
      if (!isParent) {
        handleTestimonialChange("prev");
      } else {
        handleParentTestimonialChange("prev");
      }
    } else if (dragOffset < -100 && !isLastSlide) {
      // Kéo sang trái -> hiển thị slide tiếp theo
      if (!isParent) {
        handleTestimonialChange("next");
      } else {
        handleParentTestimonialChange("next");
      }
    }
    
    setIsDragging(false);
    setActiveCarousel(null);
    
    if (isParent) {
      setParentDragOffset(0);
    } else {
      setStudentDragOffset(0);
    }
  };

  const handleMouseLeave = (isParent = false) => {
    if (activeCarousel !== (isParent ? 'parent' : 'student')) return;
    handleMouseUp(isParent);
  };

  return (
    <div className="min-h-screen">
      <section className="w-full py-12 sm:py-16 md:py-20 font-inter bg-gradient-to-t from-white to-[#EBFAF3]">
        {/* Layout Container */}
        <div className="max-w-[1200px] mx-auto flex flex-col md:flex-row px-4 items-center">
          {/* Left Content */}
          <div className="w-full md:w-[493px] flex flex-col md:mr-[32px] mb-6 sm:mb-8 md:mb-0">
            <div className="flex flex-col">
              {/* Heading and Description */}
              <div className="flex flex-col">
                <h1 className="text-[30px] sm:text-[36px] md:text-[48px] font-semibold text-[#114721] leading-tight md:leading-[60px] tracking-[-0.96px]">
                  Chinh phục Hóa học cùng Ông Ba Dạy Hóa
                </h1>
                <p className="text-[16px] sm:text-[16px] md:text-[20px] pt-3 sm:pt-4 font-normal text-[#535862] leading-[1.5] md:leading-[32px]">
                  Sẵn sàng khám phá Hóa học theo cách hoàn toàn mới? Livestream dễ hiểu, thí nghiệm thực tế, cá nhân hóa bằng AI!
                </p>
              </div>

              {/* Check Items */}
              <div className="pl-2 sm:pl-4 flex flex-col gap-4 sm:gap-5 text-[16px] sm:text-[18px] leading-[24px] sm:leading-[28px] font-semibold text-[#414651]">
                <div className="flex items-start gap-2 sm:gap-3 pt-6 sm:pt-8">
                  <div className="w-6 h-6 relative">
                    <Image
                      src={MarkerPinIcon}
                      alt="MarkerPin Icon"
                      width={24}
                      height={24}
                      className="text-mauColors/Brand/600"
                    />
                  </div>
                  <div className="flex-1">
                    <p className="">13 năm &quot;chinh chiến&quot; Hóa</p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 relative">
                    <Image
                      src={TiktokIcon}
                      alt="Tiktok icon"
                      width={24}
                      height={24}
                      className="text-mauColors/Brand/600"
                    />
                  </div>
                  <div className="flex-1">
                    <p>TikTok 400k+ Follower</p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 relative">
                    <Image
                      src={LineChartIcon}
                      alt="LineChart Icon"
                      width={24}
                      height={24}
                      className="text-mauColors/Brand/600"
                    />
                  </div>
                  <div className="flex-1">
                    <p>75% học trò đạt 7.0+</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row md:flex-row gap-3 mt-6 sm:mt-auto md:mt-8 sm:justify-center md:justify-start sm:pt-8 md:pt-0">
              <Button
                variant="primary"
                className="px-3 sm:px-4 py-2.5 sm:py-3 shadow-md text-[14px] sm:text-[16px]"
                onClick={() => {
                  gaEvent({
                    action: 'click_hero_register',
                    category: 'Homepage CTA',
                    label: 'Hero Section - Đăng ký học ngay',
                  });
                  setShowGradeSelect(true);
                }}
              >
                Đăng ký học ngay
              </Button>
              <Button
                variant="secondaryGray"
                className="px-3 sm:px-4 py-2.5 sm:py-3 text-[14px] sm:text-[16px] text-[#475569] border border-[#E2E8F0] shadow-sm"
                onClick={() => {
                  gaEvent({
                    action: 'click_hero_view_courses',
                    category: 'Homepage CTA',
                    label: 'Hero Section - Xem khóa học',
                  });
                  scrollToSection("course-header");
                }}
              >
                Xem khóa học
              </Button>
            </div>
          </div>

          {/* Right Content - Quiz Card */}
          <div className="w-full md:w-[55%] md:max-w-[675px] flex-1 p-2 sm:p-3 bg-[#FAFAFA] rounded-xl shadow-lg border border-[#E9EAEB]">
            <div className="rounded-[10px] border border-[#E9EAEB] ">
              {/* Quiz Header */}
              <div className="p-3 sm:p-4 bg-[#DCFAEC] rounded-t-[10px] flex flex-col gap-1">
                <h3 className="text-[16px] sm:text-base md:text-lg font-medium md:font-semibold text-[#334155]">
                  Bạn &quot;Pro&quot; Hóa cỡ nào? 😉
                </h3>
                <p className="text-[14px] sm:text-sm md:text-base text-[#475569]">
                  Vài câu hỏi, tìm ngay được điểm yếu
                </p>
              </div>

              {/* Quiz Content */}
              <div className="bg-white rounded-b-[10px]">
                <div className="px-3 sm:px-4 py-2 sm:py-3 flex flex-col gap-2">
                  {/* Question Badge */}
                  <div className="px-2 sm:px-3 py-1 bg-[#F0FFF7] text-[#299D55] text-[12px] sm:text-sm font-medium rounded-full border border-[#B5F2D7] inline-flex items-center w-fit">
                    Câu 1/5
                  </div>

                  {/* Question */}
                  <div className="w-full sm:max-w-[280px] flex flex-col">
                    <h3 className="text-[14px] sm:text-base font-semibold text-[#475569]">
                      Tại sao nước lại dập tắt được lửa?
                    </h3>
                  </div>

                  {/* Answer Options */}
                  <div className="space-y-1.5 sm:space-y-2 mt-1.5 sm:mt-2">
                    <label
                      className="p-2 sm:p-3 rounded-lg border border-[#E9EAEB] flex items-start gap-1.5 sm:gap-2 cursor-pointer hover:bg-gray-50"
                      onClick={() => setSelectedQuizOption("option1")}
                    >
                      <Radio
                        name="quiz-answer"
                        selected={selectedQuizOption === "option1"}
                        onChange={() => setSelectedQuizOption("option1")}
                      />
                      <span className="text-[12px] sm:text-sm font-medium text-[#475569]">
                        Vì nước lạnh hơn lửa
                      </span>
                    </label>

                    <label
                      className="p-2 sm:p-3 rounded-lg border border-[#E9EAEB] flex items-start gap-1.5 sm:gap-2 cursor-pointer hover:bg-gray-50"
                      onClick={() => setSelectedQuizOption("option2")}
                    >
                      <Radio
                        name="quiz-answer"
                        selected={selectedQuizOption === "option2"}
                        onChange={() => setSelectedQuizOption("option2")}
                      />
                      <span className="text-[12px] sm:text-sm font-medium text-[#475569]">
                        Vì nước làm ngắt nguồn oxy cân thiết cho quá trình cháy
                      </span>
                    </label>

                    <label
                      className="p-2 sm:p-3 rounded-lg border border-[#E9EAEB] flex items-start gap-1.5 sm:gap-2 cursor-pointer hover:bg-gray-50"
                      onClick={() => setSelectedQuizOption("option3")}
                    >
                      <Radio
                        name="quiz-answer"
                        selected={selectedQuizOption === "option3"}
                        onChange={() => setSelectedQuizOption("option3")}
                      />
                      <span className="text-[12px] sm:text-sm font-medium text-[#475569]">
                        Vì nước có tính axít cao
                      </span>
                    </label>

                    <label
                      className="p-2 sm:p-3 rounded-lg border border-[#E9EAEB] flex items-start gap-1.5 sm:gap-2 cursor-pointer hover:bg-gray-50"
                      onClick={() => setSelectedQuizOption("option4")}
                    >
                      <Radio
                        name="quiz-answer"
                        selected={selectedQuizOption === "option4"}
                        onChange={() => setSelectedQuizOption("option4")}
                      />
                      <span className="text-[12px] sm:text-sm font-medium text-[#475569]">
                        Vì nước có tính axít cao
                      </span>
                    </label>
                  </div>
                </div>

                {/* Quiz Footer */}
                <div className="px-3 sm:px-4 pt-1 sm:pt-2 pb-3 sm:pb-4 flex justify-center items-center">
                  <Button
                    variant="primary"
                    className="flex justify-center items-center px-2.5 sm:px-3 py-1.5 sm:py-2 gap-1 text-[12px] sm:text-sm shadow-md"
                    icon={
                      <Image
                        src={ChevronIcon}
                        alt="Chevron icon"
                        width={16}
                        height={16}
                        className="w-4 sm:w-5 h-4 sm:h-5"
                      />
                    }
                    iconPosition="right"
                    onClick={() => {
                      gaEvent({
                        action: 'click_quiz_start',
                        category: 'Homepage CTA',
                        label: 'Quiz Card - Chơi ngay luôn',
                      });
                      setShowQuizGradeSelect(true);
                    }}
                  >
                    Chơi ngay luôn
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Hidden Quiz Grade Select */}
      {showGradeSelect && (
        <GradeSelect
          onClose={() => setShowGradeSelect(false)}
          onSelectGrade={handleSelectGrade}
        />
      )}

      {/* Hidden Quiz Grade Select */}
      {showQuizGradeSelect && (
        <QuizGradeSelect
          onClose={() => setShowQuizGradeSelect(false)}
          onSectionScroll={scrollToSection}
          onSelectGrade={handleQuizGradeSelect}
        />
      )}

      {/* Khó khăn khi học Hóa Section - Thiết kế mới */}
      <section className="w-full py-10 sm:py-12 md:py-16">
        <div className="max-w-[1200px] mx-auto px-4 flex flex-col items-center">
          <div className="text-center max-w-3xl mb-10">
            <h2 className="text-2xl sm:text-3xl md:text-5xl font-semibold text-[#114721] mb-4 leading-tight md:leading-[1.2]">
              <span className="inline sm:inline-block md:inline">
                Điểm Hóa&nbsp;
              </span>
              <br className="inline sm:hidden" />
              <span className="inline sm:inline-block md:inline">
              mãi chưa bứt phá?🤔
              </span>
            </h2>
            <p className="text-base sm:text-lg text-[#334155]">
              <span></span>
              Điểm Hóa chưa như mong đợi? Tìm hiểu 3 rào cản phổ biến nhất&nbsp;
              <br className="hidden sm:inline" />
              và phương pháp để chinh phục môn học này
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 w-full">
            {/* Card 1 */}
            <div className="bg-[#F7FFF9] rounded-lg border border-[#E9EAEB] relative overflow-hidden">
              <div className="absolute top-0 right-0 w-14 h-14 flex items-center justify-center bg-[#EBFAF3] text-[#299D55] text-xl font-bold rounded-bl-xl">
                01
              </div>
              <div className="p-8 pr-20">
                <h3 className="text-xl font-semibold text-[#114721] mb-4">
                  Cảm thấy Hóa học khó hiểu và nhàm chán?
                </h3>
                <p className="text-base text-[#535862]">
                  Nhiều học sinh cảm thấy Hóa học là môn học khó hiểu với nhiều công thức phức tạp và khái niệm trừu tượng.
                </p>
              </div>
            </div>
            
            {/* Card 2 */}
            <div className="bg-[#F7FFF9] rounded-lg border border-[#E9EAEB] relative overflow-hidden">
              <div className="absolute top-0 right-0 w-14 h-14 flex items-center justify-center bg-[#EBFAF3] text-[#299D55] text-xl font-bold rounded-bl-xl">
                02
              </div>
              <div className="p-8 pr-20">
                <h3 className="text-xl font-semibold text-[#114721] mb-4">
                  Chỉ học thuộc công thức mà không hiểu rõ bản chất?
                </h3>
                <p className="text-base text-[#535862]">
                  Nhiều học sinh chỉ học thuộc công thức mà không hiểu được bản chất của các phản ứng hóa học, dẫn đến việc dễ quên và khó áp dụng vào các bài tập nâng cao.
                </p>
              </div>
            </div>
            
            {/* Card 3 */}
            <div className="bg-[#F7FFF9] rounded-lg border border-[#E9EAEB] relative overflow-hidden">
              <div className="absolute top-0 right-0 w-14 h-14 flex items-center justify-center bg-[#EBFAF3] text-[#299D55] text-xl font-bold rounded-bl-xl">
                03
              </div>
              <div className="p-8 pr-20">
                <h3 className="text-xl font-semibold text-[#114721] mb-4">
                  Muốn cải thiện điểm số nhưng không biết bắt đầu từ đâu?
                </h3>
                <p className="text-base text-[#535862]">
                  Nhiều học sinh muốn cải thiện điểm số môn Hóa nhưng không biết nên bắt đầu từ đâu, học như thế nào để hiệu quả và tiết kiệm thời gian.
                </p>
              </div>
            </div>
          </div>
          
          <div className="mt-10">
            <Button
              variant="primary"
              className="px-6 py-3 text-base shadow-md flex items-center"
              onClick={() => {
                gaEvent({
                  action: 'click_difficulty_explore_method',
                  category: 'Homepage CTA',
                  label: 'Khó khăn Section - Khám phá phương pháp học',
                });
                scrollToSection("learning-method");
              }}
            >
              Khám phá phương pháp học <span className="ml-2">›</span>
            </Button>
          </div>
        </div>
      </section>

      {/* Meet the Teacher & Testimonials Section */}
      <section className="w-full py-10 sm:py-12 md:py-16 bg-gradient-to-t from-[#EBFAF3] to-white">
        <div className="max-w-[1200px] mx-auto px-4 flex flex-col items-center gap-10 sm:gap-12 md:gap-16">
          {/* Meet the Teacher */}
          <div className="px-0 sm:px-16 md:px-0">
            <div className="text-center max-w-3xl mx-auto mb-2 sm:mb-2 md:mb-6">
              <h2 className="text-2xl sm:text-3xl md:text-5xl font-semibold text-[#114721] mb-4 leading-tight md:leading-[1.2]">
                <span className="inline sm:inline-block md:inline">
                  Gặp gỡ &quot;Thuyền trưởng&quot;&nbsp;
                </span>
                <br className="inline sm:hidden md:inline" />
                <span className="inline sm:inline-block md:inline">
                  Ông Ba Dạy Hóa ‍‍👨‍🔬
                </span>
              </h2>
              <p className="text-base sm:text-lg text-[#334155]">
                Cùng bạn khám phá Hóa học trong hành trình này
              </p>
            </div>

            <div className="w-full grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6 md:gap-8 items-center">
              <div className="rounded-xl overflow-hidden h-[300px] sm:h-[350px] md:h-[450px] bg-gray-200 order-1 md:order-1">
                <div className="w-full h-full relative">
                  <Image
                    src="/images/homepage/ilustration.png"
                    alt="Hình ảnh giáo viên"
                    fill
                    className="object-contain p-2 sm:p-4"
                    sizes="(max-width: 640px) 100vw, (max-width: 960px) 80vw, 50vw"
                  />
                </div>
              </div>

              <div className="flex flex-col gap-6 sm:gap-8 order-2 sm:order-1 md:order-2">
                <p className="text-base sm:text-lg text-[#334155]">
                  Hóa học là để khám phá, không phải để học thuộc! Trải nghiệm thí nghiệm thực tế, hiểu sâu bản chất, tự tin chinh phục mọi đề thi.
                </p>

                <div className="pl-3 sm:pl-5 flex flex-col gap-4 sm:gap-5">
                  <div className="flex items-start gap-2 sm:gap-3">
                    <div className="w-5 sm:w-6 h-5 sm:h-6 relative">
                      <Image
                        src={ChalkboardIcon}
                        alt="Chalkboard Icon"
                        width={20}
                        height={20}
                        className="text-mauColors/Brand/600"
                      />
                    </div>
                    <div className="flex-1">
                      <p className="text-base sm:text-lg font-semibold text-[#475569]">
                        13 năm kinh nghiệm giảng dạy
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start gap-2 sm:gap-3">
                    <div className="w-5 sm:w-6 h-5 sm:h-6 relative">
                      <Image
                        src={UserPlusIcon}
                        alt="UserPlus Icon"
                        width={20}
                        height={20}
                        className="text-mauColors/Brand/600"
                      />
                    </div>
                    <div className="flex-1">
                      <p className="text-base sm:text-lg font-semibold text-[#475569]">
                        300+ Học sinh đăng ký mỗi năm
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start gap-2 sm:gap-3">
                    <div className="w-5 sm:w-6 h-5 sm:h-6 relative">
                      <Image
                        src={LineChartIcon}
                        alt="LineChart Icon"
                        width={20}
                        height={20}
                        className="text-mauColors/Brand/600"
                      />
                    </div>
                    <div className="flex-1">
                      <p className="text-base sm:text-lg font-semibold text-[#475569]">
                        75% Học sinh đạt điểm 7.0+
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start gap-2 sm:gap-3">
                    <div className="w-5 sm:w-6 h-5 sm:h-6 relative">
                      <Image
                        src={LineChartIcon}
                        alt="LineChart Icon"
                        width={20}
                        height={20}
                        className="text-mauColors/Brand/600"
                      />
                    </div>
                    <div className="flex-1">
                      <p className="text-base sm:text-lg font-semibold text-[#475569]">
                        15% Học sinh chinh phục điểm 9+
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start gap-2 sm:gap-3">
                    <div className="w-5 sm:w-6 h-5 sm:h-6 relative">
                      <Image
                        src={LineChartIcon}
                        alt="LineChart Icon"
                        width={20}
                        height={20}
                        className="text-mauColors/Brand/600"
                      />
                    </div>
                    <div className="flex-1">
                      <p className="text-base sm:text-lg font-semibold text-[#475569]">
                        70% Học sinh tiếp tục đồng hành
                      </p>
                    </div>
                  </div>
                </div>
                {/* Action Buttons */}
                <div className="flex flex-col sm:flex-row md:flex-row gap-3 sm:justify-center md:justify-start">
                  <Button
                    variant="primary"
                    className="px-3 sm:px-4 py-2.5 sm:py-3 shadow-md text-[14px] sm:text-[16px]"
                    onClick={() => {
                      gaEvent({
                        action: 'click_teacher_register',
                        category: 'Homepage CTA',
                        label: 'Meet Teacher Section - Đăng ký học ngay',
                      });
                      setShowGradeSelect(true);
                    }}
                  >
                    Đăng ký học ngay
                  </Button>
                  <Button
                    variant="secondaryGray"
                    className="px-3 sm:px-4 py-2.5 sm:py-3 text-[14px] sm:text-[16px] text-[#475569] border border-[#E2E8F0] shadow-sm"
                    onClick={() => {
                      gaEvent({
                        action: 'click_teacher_view_courses',
                        category: 'Homepage CTA',
                        label: 'Meet Teacher Section - Xem khóa học',
                      });
                      scrollToSection("course");
                    }}
                  >
                    Xem khóa học
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Student Testimonials */}
          <div className="w-full">
            {/* Heading and Navigation */}
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4 sm:gap-0 mb-6 sm:mb-8">
              <h3 className="text-[20px] sm:text-[26px] md:text-[32px] font-semibold text-[#114721] leading-tight">
                Đánh giá từ học sinh
              </h3>
              <div className="flex gap-2 sm:gap-3 self-end sm:self-auto">
                {/* Student Testimonials navigation buttons */}
                <button
                  className="p-1.5 sm:p-2 md:p-3 rounded-lg bg-[#fff] border border-[#E9EAEB] bg-white shadow-sm hover:border-[#cfcece] transition-colors duration-200"
                  onClick={() => handleTestimonialChange("prev")}
                >
                  <div className="relative w-4 sm:w-5 h-4 sm:h-5 flex items-center justify-center">
                    <Image
                      src="/images/homepage/chevron-pre-left.svg"
                      alt="Previous"
                      width={16}
                      height={16}
                      className="hidden"
                    />
                    <Image
                      src="/images/homepage/chevron-active-pre-left.svg"
                      alt="Previous Active"
                      width={16}
                      height={16}
                      className="block"
                    />
                  </div>
                </button>
                <button
                  className="p-1.5 sm:p-2 md:p-3 rounded-lg bg-[#fff] border border-[#E9EAEB] bg-white shadow-sm hover:border-[#cfcece] transition-colors duration-200"
                  onClick={() => handleTestimonialChange("next")}
                >
                  <div className="relative w-4 sm:w-5 h-4 sm:h-5 flex items-center justify-center">
                    <Image
                      src="/images/homepage/chevron-next-right.svg"
                      alt="Next"
                      width={16}
                      height={16}
                      className="hidden"
                    />
                    <Image
                      src="/images/homepage/chevron-active-next-right.svg"
                      alt="Next Active"
                      width={16}
                      height={16}
                      className="block"
                    />
                  </div>
                </button>
              </div>
            </div>

            {/* Testimonials Carousel */}
            {!isLoading && studentTestimonials.length > 0 && (
              <>
                <div className="w-full overflow-hidden">
                  <div
                    ref={studentCarouselRef}
                    className="flex transition-transform duration-300 ease-in-out select-none cursor-grab active:cursor-grabbing"
                    style={{
                      transform: `translateX(calc(-${activeTestimonial * (100 / itemsPerPage)}% + ${studentDragOffset}px))`,
                      transition: isDragging && activeCarousel === 'student' ? 'none' : 'transform 300ms ease-in-out'
                    }}
                    onMouseDown={(e) => handleMouseDown(e, studentCarouselRef, false)}
                    onMouseMove={(e) => handleMouseMove(e, studentCarouselRef, false)}
                    onMouseUp={() => handleMouseUp(false)}
                    onMouseLeave={() => handleMouseLeave(false)}
                    onTouchStart={(e) => handleTouchStart(e, studentCarouselRef, false)}
                    onTouchMove={(e) => handleTouchMove(e, studentCarouselRef, false)}
                    onTouchEnd={() => handleMouseUp(false)}
                  >
                    {/* Testimonial Cards*/}
                    {studentTestimonials.map((testimonial) => {
                      return (
                        <div
                          key={testimonial.id}
                          className={`px-2 md:px-3 select-none ${
                            itemsPerPage === 1 
                              ? "min-w-full" 
                              : itemsPerPage === 2 
                                ? "min-w-[50%]" 
                                : "min-w-[33.333%]"
                          }`}
                        >
                          <div className="bg-[#fff] rounded-[20px] border border-[#D5D7DA] p-4 sm:p-6 md:p-8 flex flex-col h-full select-none">
                            {/* Student Info */}
                            <div className="flex items-center gap-3 sm:gap-4 mb-4 sm:mb-8">
                              <div className="w-10 h-10 rounded-full overflow-hidden relative">
                               {testimonial.avatar && testimonial.avatar.length > 0 ? (
                                  <Image
                                    src={`${process.env.NEXT_PUBLIC_STRAPI_URL || 'http://localhost:1337'}${testimonial.avatar[0].url}`}
                                    alt={testimonial.name || "Student"}
                                    width={40}
                                    height={40}
                                    className="w-10 h-10 object-cover"
                                  />
                                ) : (
                                   <Image
                                    src="/images/homepage/student.png"
                                     alt={testimonial.name || "Student"}
                                     width={40}
                                     height={40}
                                     className="object-cover"
                                   />
                                )}
                              </div>
                              <div>
                                <h4 className="text-base sm:text-lg font-semibold text-[#181D27]">
                                  {testimonial.name || "Student"}
                                </h4>
                                {testimonial.school && (
                                  <p className="text-sm sm:text-base text-[#717680]">
                                    {testimonial.school}
                                  </p>
                                )}
                              </div>
                            </div>

                            {/* Testimonial Content */}
                            <div className="flex-1 mb-3 sm:mb-4">
                              <p className="text-sm sm:text-base text-[#181D27]">
                                &ldquo;{testimonial.review || ""}&rdquo;
                              </p>
                            </div>

                            {/* Score - Chỉ hiển thị cho học sinh */}
                            {testimonial.role === "student" && testimonial.score && (
                              <div className="flex items-center gap-1.5 sm:gap-2 text-[#299D55]">
                                <Image
                                  src="/images/homepage/award-03.svg"
                                  alt="Award"
                                  width={16}
                                  height={16}
                                  className="w-4 sm:w-5 h-4 sm:h-5"
                                />
                                <span className="text-sm sm:text-base font-semibold">
                                  {testimonial.scoreType === 'hoc-ki' ? 'Điểm thi học kì: ' : 'Điểm thi THPT: '}
                                  {testimonial.score.replace('d', '').replace('_', '.')}
                                </span>
                              </div>
                            )}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>

                {/* Pagination Dots */}
                <div className="flex justify-center mt-6 sm:mt-8 gap-2 sm:gap-3">
                  {Array.from({ length: totalStudentPages }).map((_, index) => (
                    <button
                      key={index}
                      className={`h-[8px] sm:h-[10px] flex items-center justify-center ${
                        Math.floor(activeTestimonial / itemsPerPage) === index
                          ? "w-[20px] sm:w-[28px] rounded-[9999px] bg-[#299D55]"
                          : "w-[8px] sm:w-[10px] rounded-full bg-[#E9EAEB]"
                      } transition-all duration-300`}
                      onClick={() => setActiveTestimonial(index * itemsPerPage)}
                    />
                  ))}
                </div>
              </>
            )}
          </div>
        </div>

        {/* Parent Testimonials */}
        <div className="w-full py-10 sm:py-16 bg-white">
          <div className="max-w-[1200px] mx-auto px-4">
            <div className="w-full">
              {/* Heading and Navigation */}
              <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4 sm:gap-0 mb-6 sm:mb-8">
                <h3 className="text-[20px] sm:text-[26px] md:text-[32px] font-semibold text-[#114721] leading-tight">
                  Phụ huynh có nhận xét gì?
                </h3>
                <div className="flex gap-2 sm:gap-3 self-end sm:self-auto">
                  {/* Parent Testimonials navigation buttons */}
                  <button
                    className="p-1.5 sm:p-2 md:p-3 rounded-lg bg-[#fff] border border-[#E9EAEB] bg-white shadow-sm hover:border-[#cfcece] transition-colors duration-200"
                    onClick={() => handleParentTestimonialChange("prev")}
                  >
                    <div className="relative w-4 sm:w-5 h-4 sm:h-5 flex items-center justify-center">
                      <Image
                        src="/images/homepage/chevron-pre-left.svg"
                        alt="Previous"
                        width={16}
                        height={16}
                        className="hidden"
                      />
                      <Image
                        src="/images/homepage/chevron-active-pre-left.svg"
                        alt="Previous Active"
                        width={16}
                        height={16}
                        className="block"
                      />
                    </div>
                  </button>
                  <button
                    className="p-1.5 sm:p-2 md:p-3 rounded-lg bg-[#fff] border border-[#E9EAEB] bg-white shadow-sm hover:border-[#cfcece] transition-colors duration-200"
                    onClick={() => handleParentTestimonialChange("next")}
                  >
                    <div className="relative w-4 sm:w-5 h-4 sm:h-5 flex items-center justify-center">
                      <Image
                        src="/images/homepage/chevron-next-right.svg"
                        alt="Next"
                        width={16}
                        height={16}
                        className="hidden"
                      />
                      <Image
                        src="/images/homepage/chevron-active-next-right.svg"
                        alt="Next Active"
                        width={16}
                        height={16}
                        className="block"
                      />
                    </div>
                  </button>
                </div>
              </div>

              {/* Testimonials Carousel */}
              {!isLoading && parentTestimonials.length > 0 && (
                <>
                  <div className="w-full overflow-hidden">
                    <div
                      ref={parentCarouselRef}
                      className="flex transition-transform duration-300 ease-in-out select-none cursor-grab active:cursor-grabbing"
                      style={{
                        transform: `translateX(calc(-${activeParentTestimonial * (100 / itemsPerPage)}% + ${parentDragOffset}px))`,
                        transition: isDragging && activeCarousel === 'parent' ? 'none' : 'transform 300ms ease-in-out'
                      }}
                      onMouseDown={(e) => handleMouseDown(e, parentCarouselRef, true)}
                      onMouseMove={(e) => handleMouseMove(e, parentCarouselRef, true)}
                      onMouseUp={() => handleMouseUp(true)}
                      onMouseLeave={() => handleMouseLeave(true)}
                      onTouchStart={(e) => handleTouchStart(e, parentCarouselRef, true)}
                      onTouchMove={(e) => handleTouchMove(e, parentCarouselRef, true)}
                      onTouchEnd={() => handleMouseUp(true)}
                    >
                      {/* Parent Testimonial Cards */}
                      {parentTestimonials.map((testimonial) => {
                        return (
                          <div
                            key={testimonial.id}
                            className={`px-2 md:px-3 select-none ${
                              itemsPerPage === 1 
                                ? "min-w-full" 
                                : itemsPerPage === 2 
                                  ? "min-w-[50%]" 
                                  : "min-w-[33.333%]"
                            }`}
                          >
                            <div className="bg-[#fff] rounded-[20px] border border-[#D5D7DA] p-4 sm:p-6 md:p-8 flex flex-col h-full select-none">
                              {/* Parent Info - Không hiển thị avatar */}
                              <div className="flex items-center gap-3 sm:gap-4 mb-4 sm:mb-8">
                                <div>
                                  <h4 className="text-base sm:text-lg font-semibold text-[#181D27]">
                                    {testimonial.name || "Phụ huynh"}
                                  </h4>
                                  {testimonial.subtitle && (
                                    <p className="text-sm sm:text-base text-[#717680]">
                                      {testimonial.subtitle}
                                    </p>
                                  )}
                                </div>
                              </div>

                              {/* Testimonial Content */}
                              <div className="flex-1 mb-3 sm:mb-4">
                                <p className="text-sm sm:text-base text-[#181D27]">
                                  &ldquo;{testimonial.review || ""}&rdquo;
                                </p>
                              </div>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>

                  {/* Pagination Dots */}
                  <div className="flex justify-center mt-6 sm:mt-8 gap-2 sm:gap-3">
                    {Array.from({ length: totalParentPages }).map((_, index) => (
                      <button
                        key={index}
                        className={`h-[8px] sm:h-[10px] flex items-center justify-center ${
                          Math.floor(activeParentTestimonial / itemsPerPage) === index
                            ? "w-[20px] sm:w-[28px] rounded-[9999px] bg-[#299D55]"
                            : "w-[8px] sm:w-[10px] rounded-full bg-[#E9EAEB]"
                        } transition-all duration-300`}
                        onClick={() => setActiveParentTestimonial(index * itemsPerPage)}
                      />
                    ))}
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="learning-method-header" className="w-full py-10 sm:py-16 bg-white">
        <div
          id="learning-method"
          className="max-w-[1200px] mx-auto px-4 flex flex-col items-center gap-8 md:gap-12"
        >
          {/* Heading and supporting text */}
          <div className="text-center max-w-3xl">
            <h2 className="text-2xl sm:text-3xl md:text-5xl font-semibold text-[#114721] mb-4 leading-tight md:leading-[1.2]">
              <span className="inline sm:inline-block md:inline">
                Hiểu bản chất&nbsp;
              </span>
              <br className="inline sm:hidden md:inline" />
              <span className="inline sm:inline-block md:inline">
                Làm chủ Hóa học 🧪
              </span>
            </h2>
            <p className="text-lg text-[#252B37]">
              Phương pháp toàn diện giúp học sinh hiểu sâu bản chất, học tập thú
              vị và đạt điểm cao cùng với sự trợ giúp từ AI
            </p>
          </div>

          {/* Features Row 1 */}
          <div className="w-full grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4 md:gap-6">
            {/* Feature 1 */}
            <div className="bg-white rounded-lg border border-[#E9EAEB] p-6 flex flex-col items-center gap-5">
              <div className="w-10 h-10 rounded-lg bg-gradient-to-b from-[#EBFAF3] to-[#FAFFFD] flex items-center justify-center border border-[#DCFAEC] shadow-sm">
                <Image
                  src="/images/homepage/Discord.svg"
                  alt="Livestream"
                  width={24}
                  height={24}
                />
              </div>
              <div className="text-center">
                <h3 className="text-xl font-semibold text-[#181D27] mb-2">
                  Livestream tương tác
                </h3>
                <p className="text-base text-[#535862]">
                  Tham gia học trực tuyến qua Discord, tương tác và nhận phản
                  hồi trực tiếp.
                </p>
              </div>
            </div>

            {/* Feature 2 */}
            <div className="bg-white rounded-lg border border-[#E9EAEB] p-6 flex flex-col items-center gap-5">
              <div className="w-10 h-10 rounded-lg bg-gradient-to-b from-[#EBFAF3] to-[#FAFFFD] flex items-center justify-center border border-[#DCFAEC] shadow-sm">
                <Image
                  src="/images/homepage/beaker-02.svg"
                  alt="Thí nghiệm"
                  width={24}
                  height={24}
                />
              </div>
              <div className="text-center">
                <h3 className="text-xl font-semibold text-[#181D27] mb-2">
                  Thí nghiệm trực quan
                </h3>
                <p className="text-base text-[#535862]">
                  Quan sát các thí nghiệm được thực hiện trực tiếp, giúp hiểu rõ
                  bản chất các phản ứng.
                </p>
              </div>
            </div>

            {/* Feature 3 */}
            <div className="bg-white rounded-lg border border-[#E9EAEB] p-6 flex flex-col items-center gap-5">
              <div className="w-10 h-10 rounded-lg bg-gradient-to-b from-[#EBFAF3] to-[#FAFFFD] flex items-center justify-center border border-[#DCFAEC] shadow-sm">
                <Image
                  src="/images/homepage/play-circle.svg"
                  alt="Xem lại bài giảng"
                  width={24}
                  height={24}
                />
              </div>
              <div className="text-center">
                <h3 className="text-xl font-semibold text-[#181D27] mb-2">
                  Xem lại bài giảng
                </h3>
                <p className="text-base text-[#535862]">
                  Kho video bài giảng đã Livestream để ôn tập và củng cố kiến
                  thức bất cứ lúc nào.
                </p>
              </div>
            </div>

            {/* Feature 4 */}
            <div className="bg-white rounded-lg border border-[#E9EAEB] p-6 flex flex-col items-center gap-5">
              <div className="w-10 h-10 rounded-lg bg-gradient-to-b from-[#EBFAF3] to-[#FAFFFD] flex items-center justify-center border border-[#DCFAEC] shadow-sm">
                <Image
                  src="/images/homepage/truck-01.svg"
                  alt="Tài liệu"
                  width={24}
                  height={24}
                />
              </div>
              <div className="text-center">
                <h3 className="text-xl font-semibold text-[#181D27] mb-2">
                  Tài liệu giao đến nhà
                </h3>
                <p className="text-base text-[#535862]">
                  Bộ bài tập và tài liệu học tập bản cứng độc quyền. Cập nhật
                  đầy đủ kiến thức mới.
                </p>
              </div>
            </div>
          </div>

          {/* Features Row 2 */}
          <div className="w-full grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4 md:gap-6">
            {/* Feature 5 */}
            <div className="bg-white rounded-lg border border-[#E9EAEB] p-6 flex flex-col items-center gap-5">
              <div className="w-10 h-10 rounded-lg bg-gradient-to-b from-[#EBFAF3] to-[#FAFFFD] flex items-center justify-center border border-[#DCFAEC] shadow-sm">
                <Image
                  src="/images/homepage/telescope.svg"
                  alt="Ví dụ thực tế"
                  width={24}
                  height={24}
                />
              </div>
              <div className="text-center">
                <h3 className="text-xl font-semibold text-[#181D27] mb-2">
                  Ví dụ thực tế
                </h3>
                <p className="text-base text-[#535862]">
                  Liên hệ kiến thức Hóa học với các ví dụ và ứng dụng thực tế
                  trong đời sống hàng ngày.
                </p>
              </div>
            </div>

            {/* Feature 6 */}
            <div className="bg-white rounded-lg border border-[#E9EAEB] p-6 flex flex-col items-center gap-5">
              <div className="w-10 h-10 rounded-lg bg-gradient-to-b from-[#EBFAF3] to-[#FAFFFD] flex items-center justify-center border border-[#DCFAEC] shadow-sm">
                <Image
                  src="/images/homepage/message-chat-square.svg"
                  alt="Hỏi đáp"
                  width={24}
                  height={24}
                />
              </div>
              <div className="text-center">
                <h3 className="text-xl font-semibold text-[#181D27] mb-2">
                  Hỏi đáp tức thì
                </h3>
                <p className="text-base text-[#535862]">
                  Đặt câu hỏi và nhận giải đáp nhanh chóng về bài học, bài tập
                  qua kênh Discord
                </p>
              </div>
            </div>

            {/* Feature 7 */}
            <div className="bg-white rounded-lg border border-[#E9EAEB] p-6 flex flex-col items-center gap-5">
              <div className="w-10 h-10 rounded-lg bg-gradient-to-b from-[#EBFAF3] to-[#FAFFFD] flex items-center justify-center border border-[#DCFAEC] shadow-sm">
                <Image
                  src="/images/homepage/ai.svg"
                  alt="Tích hợp AI"
                  width={24}
                  height={24}
                />
              </div>
              <div className="text-center">
                <h3 className="text-xl font-semibold text-[#181D27] mb-2">
                  Tích hợp AI
                </h3>
                <p className="text-base text-[#535862]">
                  Phân tích kết quả học tập, đề xuất lộ trình và nội dung ôn tập
                  cá nhân hóa.
                </p>
              </div>
            </div>

            {/* Feature 8 */}
            <div className="bg-white rounded-lg border border-[#E9EAEB] p-6 flex flex-col items-center gap-5">
              <div className="w-10 h-10 rounded-lg bg-gradient-to-b from-[#EBFAF3] to-[#FAFFFD] flex items-center justify-center border border-[#DCFAEC] shadow-sm">
                <Image
                  src="/images/homepage/gaming-pad-01.svg"
                  alt="Tích hợp Game"
                  width={24}
                  height={24}
                />
              </div>
              <div className="text-center">
                <h3 className="text-xl font-semibold text-[#181D27] mb-2">
                  Tích hợp Game
                </h3>
                <p className="text-base text-[#535862]">
                  Làm bài tập, bài kiểm tra thông qua hệ thống trò chơi, theo
                  dõi tiến độ hiệu quả.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Roadmap Section */}
      <section id="course-header" className="w-full py-10 sm:py-16 bg-white">
        <div
          id="course"
          className="max-w-[1200px] mx-auto px-4 sm:px-4 flex flex-col items-center gap-8 md:gap-12"
        >
          {/* Heading and supporting text */}
          <div className="text-center max-w-3xl">
            <h2 className="text-2xl sm:text-3xl md:text-5xl font-semibold text-[#114721] mb-4 leading-tight md:leading-[1.2]">
              <span className="inline sm:inline-block md:inline">
                Lộ trình &ldquo;phá đảo&rdquo;&nbsp;
              </span>
              <br className="inline sm:hidden md:inline" />
              <span className="inline sm:inline-block md:inline">
                Hóa học THPT 📈
              </span>
            </h2>
            <p className="text-lg text-[#252B37]">
              Thiết kế riêng cho từng lớp, giúp bạn tăng tốc về đích! 👇👇👇
            </p>
          </div>

          {/* Course Tabs */}
          <div className="w-full bg-[#F0FFF7] rounded-xl border border-[#E9EAEB] p-4 flex flex-col gap-3">
            {/* Tabs */}
            <div className="grid grid-cols-3 w-full mx-auto">
              <button
                className={`${
                  activeGrade === "lop10"
                    ? "bg-[#fff] text-[#414651] font-semibold rounded-lg border border-[#E9EAEB]"
                    : "text-[#717680]"
                } px-4 py-2 h-11`}
                onClick={() => handleGradeChange("lop10")}
              >
                Lớp 10
              </button>
              <button
                className={`${
                  activeGrade === "lop11"
                    ? "bg-[#fff] text-[#414651] font-semibold rounded-lg border border-[#E9EAEB]"
                    : "text-[#717680]"
                } px-4 py-2 h-11`}
                onClick={() => handleGradeChange("lop11")}
              >
                Lớp 11
              </button>
              <button
                className={`${
                  activeGrade === "lop12"
                    ? "bg-[#fff] text-[#414651] font-semibold rounded-lg border border-[#E9EAEB]"
                    : "text-[#717680]"
                } px-4 py-2 h-11`}
                onClick={() => handleGradeChange("lop12")}
              >
                Lớp 12
              </button>
            </div>

            {/* Course Content */}
            <div className="w-full bg-[#fff] rounded-lg border border-[#E9EAEB] p-6 sm:p-12 flex flex-col sm:flex-row gap-6 sm:gap-8">
              {activeGrade === "lop10" && (
                <>
                  {/* Left Column - Features */}
                  <div className="flex-1 bg-[#fff]">
                    <h3 className="text-xl text-center sm:text-start sm:text-2xl md:text-2xl font-semibold text-[#114721] mb-5">
                      Livestream Hoá 10
                    </h3>
                    <div className="flex flex-col gap-3 mb-6 sm:mb-10">
                      <div className="flex items-start gap-2">
                        <Image
                          src="/images/homepage/check.svg"
                          alt="check"
                          width={24}
                          height={24}
                        />
                        <span className="text-base sm:text-lg font-semibold text-[#414651]">
                          2 buổi học/tuần (90 phút/buổi)
                        </span>
                      </div>
                      <div className="flex items-start gap-2">
                        <Image
                          src="/images/homepage/check.svg"
                          alt="check"
                          width={24}
                          height={24}
                        />
                        <span className="text-base sm:text-lg font-semibold text-[#414651]">
                          Bộ tài liệu bản cứng độc quyền
                        </span>
                      </div>
                      <div className="flex items-start gap-2">
                        <Image
                          src="/images/homepage/check.svg"
                          alt="check"
                          width={24}
                          height={24}
                        />
                        <span className="text-base sm:text-lg font-semibold text-[#414651]">
                          Giải đáp thắc mắc trong vòng 20 phút
                        </span>
                      </div>
                      <div className="flex items-start gap-2">
                        <Image
                          src="/images/homepage/check.svg"
                          alt="check"
                          width={24}
                          height={24}
                        />
                        <span className="text-base sm:text-lg font-semibold text-[#414651]">
                          Bài kiểm tra sau mỗi chương
                        </span>
                      </div>
                      <div className="flex items-start gap-2">
                        <Image
                          src="/images/homepage/check.svg"
                          alt="check"
                          width={24}
                          height={24}
                        />
                        <span className="text-base sm:text-lg font-semibold text-[#414651]">
                          Xem lại bài giảng không giới hạn
                        </span>
                      </div>
                    </div>
                    <Button
                      variant="primary"
                      className="hidden sm:inline-block px-4 sm:px-5 py-2.5 sm:py-3 text-[14px] sm:text-[16px] shadow-md"
                      onClick={() =>
                        (window.location.href =
                          "/khoa-hoc/khoa-hoc-livestream-hoa-hoc-lop-10")
                      }
                    >
                      Chinh phục Hóa 10 ngay
                    </Button>
                  </div>

                  {/* Right Column - Content */}
                  <div className="flex-1 flex flex-col gap-3 sm:pl-5">
                    <h4 className="text-xl font-semibold text-[#252B37] mb-3">
                      Nội dung chính:
                    </h4>
                    <ul className="text-base sm:text-lg text-[#414651] space-y-2 list-none">
                      <li className="flex items-start">
                        <div className="w-6 h-6 flex items-center justify-center mr-2">
                          <Image
                            src="/images/homepage/_Dot.svg"
                            alt="dot"
                            width={10}
                            height={10}
                          />
                        </div>
                        <span>Cấu tạo nguyên tử: Hạt, số hiệu, khối.</span>
                      </li>
                      <li className="flex items-start">
                        <div className="w-6 h-6 flex items-center justify-center mr-2">
                          <Image
                            src="/images/homepage/_Dot.svg"
                            alt="dot"
                            width={10}
                            height={10}
                          />
                        </div>
                        <span>Vỏ nguyên tử: Lớp, phân lớp, cấu hình e.</span>
                      </li>
                      <li className="flex items-start">
                        <div className="w-6 h-6 flex items-center justify-center mr-2">
                          <Image
                            src="/images/homepage/_Dot.svg"
                            alt="dot"
                            width={10}
                            height={10}
                          />
                        </div>
                        <span>Bảng tuần hoàn: Cấu trúc, quy luật.</span>
                      </li>
                      <li className="flex items-start">
                        <div className="w-6 h-6 flex items-center justify-center mr-2">
                          <Image
                            src="/images/homepage/_Dot.svg"
                            alt="dot"
                            width={10}
                            height={10}
                          />
                        </div>
                        <span>Liên kết hóa học: Ion, cộng hóa trị.</span>
                      </li>
                      <li className="flex items-start">
                        <div className="w-6 h-6 flex items-center justify-center mr-2">
                          <Image
                            src="/images/homepage/_Dot.svg"
                            alt="dot"
                            width={10}
                            height={10}
                          />
                        </div>
                        <span>Phản ứng oxi hóa – khử: Số oxi hóa.</span>
                      </li>
                      <li className="flex items-start">
                        <div className="w-6 h-6 flex items-center justify-center mr-2">
                          <Image
                            src="/images/homepage/_Dot.svg"
                            alt="dot"
                            width={10}
                            height={10}
                          />
                        </div>
                        <span>Năng lượng hóa học: Enthalpy, tỏa/thu.</span>
                      </li>
                      <li className="flex items-start">
                        <div className="w-6 h-6 flex items-center justify-center mr-2">
                          <Image
                            src="/images/homepage/_Dot.svg"
                            alt="dot"
                            width={10}
                            height={10}
                          />
                        </div>
                        <span>Tốc độ phản ứng: Khái niệm, yếu tố.</span>
                      </li>
                      <li className="flex items-start">
                        <div className="w-6 h-6 flex items-center justify-center mr-2">
                          <Image
                            src="/images/homepage/_Dot.svg"
                            alt="dot"
                            width={10}
                            height={10}
                          />
                        </div>
                        <span>Nhóm Halogen: Tính chất, hợp chất.</span>
                      </li>
                    </ul>
                    <Button
                      variant="primary"
                      className="sm:hidden px-4 sm:px-5 py-2.5 sm:py-3 text-[14px] sm:text-[16px] shadow-md mt-4"
                      onClick={() =>
                        (window.location.href =
                          "/khoa-hoc/khoa-hoc-livestream-hoa-hoc-lop-10")
                      }
                    >
                      Chinh phục Hóa 10 ngay
                    </Button>
                  </div>
                </>
              )}

              {activeGrade === "lop11" && (
                <>
                  {/* Left Column - Features */}
                  <div className="flex-1 bg-[#fff]">
                    <h3 className="text-xl text-center sm:text-start sm:text-2xl md:text-2xl font-semibold text-[rgb(17,71,33)] mb-5">
                      Livestream Hoá 11
                    </h3>
                    <div className="flex flex-col gap-3 mb-6 sm:mb-10">
                      <div className="flex items-start gap-2">
                        <Image
                          src="/images/homepage/check.svg"
                          alt="check"
                          width={24}
                          height={24}
                        />
                        <span className="text-base sm:text-lg font-semibold text-[#414651]">
                          2 buổi học/tuần (90 phút/buổi)
                        </span>
                      </div>
                      <div className="flex items-start gap-2">
                        <Image
                          src="/images/homepage/check.svg"
                          alt="check"
                          width={24}
                          height={24}
                        />
                        <span className="text-base sm:text-lg font-semibold text-[#414651]">
                          Bộ tài liệu bản cứng độc quyền
                        </span>
                      </div>
                      <div className="flex items-start gap-2">
                        <Image
                          src="/images/homepage/check.svg"
                          alt="check"
                          width={24}
                          height={24}
                        />
                        <span className="text-base sm:text-lg font-semibold text-[#414651]">
                          Giải đáp thắc mắc trong vòng 20 phút
                        </span>
                      </div>
                      <div className="flex items-start gap-2">
                        <Image
                          src="/images/homepage/check.svg"
                          alt="check"
                          width={24}
                          height={24}
                        />
                        <span className="text-base sm:text-lg font-semibold text-[#414651]">
                          Bài kiểm tra sau mỗi chương
                        </span>
                      </div>
                      <div className="flex items-start gap-2">
                        <Image
                          src="/images/homepage/check.svg"
                          alt="check"
                          width={24}
                          height={24}
                        />
                        <span className="text-base sm:text-lg font-semibold text-[#414651]">
                          Xem lại bài giảng không giới hạn
                        </span>
                      </div>
                    </div>
                    <Button
                      variant="primary"
                      className="hidden sm:inline-block px-4 sm:px-5 py-2.5 sm:py-3 text-[14px] sm:text-[16px] shadow-md"
                      onClick={() =>
                        (window.location.href =
                          "/khoa-hoc/khoa-hoc-livestream-hoa-hoc-lop-11")
                      }
                    >
                      Chinh phục Hóa 11 ngay
                    </Button>
                  </div>

                  {/* Right Column - Content */}
                  <div className="flex-1 flex flex-col gap-3 sm:pl-5">
                    <h4 className="text-xl font-semibold text-[#252B37] mb-3">
                      Nội dung chính:
                    </h4>
                    <ul className="text-base sm:text-lg text-[#414651] space-y-2 list-none">
                      <li className="flex items-start">
                        <div className="w-6 h-6 flex items-center justify-center mr-2">
                          <Image
                            src="/images/homepage/_Dot.svg"
                            alt="dot"
                            width={10}
                            height={10}
                          />
                        </div>
                        <span>Cân bằng hóa học: Thuận nghịch, hằng số.</span>
                      </li>
                      <li className="flex items-start">
                        <div className="w-6 h-6 flex items-center justify-center mr-2">
                          <Image
                            src="/images/homepage/_Dot.svg"
                            alt="dot"
                            width={10}
                            height={10}
                          />
                        </div>
                        <span>Sự điện li: Acid-base, pH, chuẩn độ.</span>
                      </li>
                      <li className="flex items-start">
                        <div className="w-6 h-6 flex items-center justify-center mr-2">
                          <Image
                            src="/images/homepage/_Dot.svg"
                            alt="dot"
                            width={10}
                            height={10}
                          />
                        </div>
                        <span>Nitrogen, Sulfur: Tính chất, hợp chất.</span>
                      </li>
                      <li className="flex items-start">
                        <div className="w-6 h-6 flex items-center justify-center mr-2">
                          <Image
                            src="/images/homepage/_Dot.svg"
                            alt="dot"
                            width={10}
                            height={10}
                          />
                        </div>
                        <span>Đại cương hữu cơ: Khái niệm, công thức.</span>
                      </li>
                      <li className="flex items-start">
                        <div className="w-6 h-6 flex items-center justify-center mr-2">
                          <Image
                            src="/images/homepage/_Dot.svg"
                            alt="dot"
                            width={10}
                            height={10}
                          />
                        </div>
                        <span>Hydrocarbon: Alkane, alkene, alkyne, thơm.</span>
                      </li>
                      <li className="flex items-start">
                        <div className="w-6 h-6 flex items-center justify-center mr-2">
                          <Image
                            src="/images/homepage/_Dot.svg"
                            alt="dot"
                            width={10}
                            height={10}
                          />
                        </div>
                        <span>Dẫn xuất Halogen, Alcohol, Phenol.</span>
                      </li>
                      <li className="flex items-start">
                        <div className="w-6 h-6 flex items-center justify-center mr-2">
                          <Image
                            src="/images/homepage/_Dot.svg"
                            alt="dot"
                            width={10}
                            height={10}
                          />
                        </div>
                        <span>Carbonyl, Carboxylic Acid: Tính chất.</span>
                      </li>
                    </ul>
                    <Button
                      variant="primary"
                      className="sm:hidden px-4 sm:px-5 py-2.5 sm:py-3 text-[14px] sm:text-[16px] shadow-md mt-4"
                      onClick={() =>
                        (window.location.href =
                          "/khoa-hoc/khoa-hoc-livestream-hoa-hoc-lop-11")
                      }
                    >
                      Chinh phục Hóa 11 ngay
                    </Button>
                  </div>
                </>
              )}

              {activeGrade === "lop12" && (
                <>
                  {/* Left Column - Features */}
                  <div className="flex-1 bg-[#fff]">
                    <h3 className="text-xl text-center sm:text-start sm:text-2xl md:text-2xl font-semibold text-[#114721] mb-5">
                      Livestream Hoá 12
                    </h3>
                    <div className="flex flex-col gap-3 mb-6 sm:mb-10">
                      <div className="flex items-start gap-2">
                        <Image
                          src="/images/homepage/check.svg"
                          alt="check"
                          width={24}
                          height={24}
                        />
                        <span className="text-base sm:text-lg font-semibold text-[#414651]">
                          3 buổi học/tuần (90 phút/buổi)
                        </span>
                      </div>
                      <div className="flex items-start gap-2">
                        <Image
                          src="/images/homepage/check.svg"
                          alt="check"
                          width={24}
                          height={24}
                        />
                        <span className="text-base sm:text-lg font-semibold text-[#414651]">
                          Bộ tài liệu bản cứng độc quyền
                        </span>
                      </div>
                      <div className="flex items-start gap-2">
                        <Image
                          src="/images/homepage/check.svg"
                          alt="check"
                          width={24}
                          height={24}
                        />
                        <span className="text-base sm:text-lg font-semibold text-[#414651]">
                          Giải đáp thắc mắc trong vòng 20 phút
                        </span>
                      </div>
                      <div className="flex items-start gap-2">
                        <Image
                          src="/images/homepage/check.svg"
                          alt="check"
                          width={24}
                          height={24}
                        />
                        <span className="text-base sm:text-lg font-semibold text-[#414651]">
                          Bài kiểm tra sau mỗi chương
                        </span>
                      </div>
                      <div className="flex items-start gap-2">
                        <Image
                          src="/images/homepage/check.svg"
                          alt="check"
                          width={24}
                          height={24}
                        />
                        <span className="text-base sm:text-lg font-semibold text-[#414651]">
                          Xem lại bài giảng không giới hạn
                        </span>
                      </div>
                    </div>
                    <Button
                      variant="primary"
                      className="hidden sm:inline-block px-4 sm:px-5 py-2.5 sm:py-3 text-[14px] sm:text-[16px] shadow-md"
                      onClick={() =>
                        (window.location.href =
                          "/khoa-hoc/khoa-hoc-livestream-hoa-hoc-lop-12")
                      }
                    >
                      Chinh phục Hóa 12 ngay
                    </Button>
                  </div>

                  {/* Right Column - Content */}
                  <div className="flex-1 flex flex-col gap-3 sm:pl-5">
                    <h4 className="text-xl font-semibold text-[#252B37] mb-3">
                      Nội dung chính:
                    </h4>
                    <ul className="text-base sm:text-lg text-[#414651] space-y-2 list-none">
                      <li className="flex items-start">
                        <div className="w-6 h-6 flex items-center justify-center mr-2">
                          <Image
                            src="/images/homepage/_Dot.svg"
                            alt="dot"
                            width={10}
                            height={10}
                          />
                        </div>
                        <span>Ester, Lipid: Cấu tạo, thủy phân.</span>
                      </li>
                      <li className="flex items-start">
                        <div className="w-6 h-6 flex items-center justify-center mr-2">
                          <Image
                            src="/images/homepage/_Dot.svg"
                            alt="dot"
                            width={10}
                            height={10}
                          />
                        </div>
                        <span>Carbohydrate: Glucose, sucrose, tinh bột.</span>
                      </li>
                      <li className="flex items-start">
                        <div className="w-6 h-6 flex items-center justify-center mr-2">
                          <Image
                            src="/images/homepage/_Dot.svg"
                            alt="dot"
                            width={10}
                            height={10}
                          />
                        </div>
                        <span>Amine, Amino Acid, Protein: Cấu trúc.</span>
                      </li>
                      <li className="flex items-start">
                        <div className="w-6 h-6 flex items-center justify-center mr-2">
                          <Image
                            src="/images/homepage/_Dot.svg"
                            alt="dot"
                            width={10}
                            height={10}
                          />
                        </div>
                        <span>Polymer: Khái niệm, phân loại, ứng dụng.</span>
                      </li>
                      <li className="flex items-start">
                        <div className="w-6 h-6 flex items-center justify-center mr-2">
                          <Image
                            src="/images/homepage/_Dot.svg"
                            alt="dot"
                            width={10}
                            height={10}
                          />
                        </div>
                        <span>Đại cương kim loại: Tính chất chung.</span>
                      </li>
                      <li className="flex items-start">
                        <div className="w-6 h-6 flex items-center justify-center mr-2">
                          <Image
                            src="/images/homepage/_Dot.svg"
                            alt="dot"
                            width={10}
                            height={10}
                          />
                        </div>
                        <span>Dãy điện hóa: Ý nghĩa, ăn mòn.</span>
                      </li>
                      <li className="flex items-start">
                        <div className="w-6 h-6 flex items-center justify-center mr-2">
                          <Image
                            src="/images/homepage/_Dot.svg"
                            alt="dot"
                            width={10}
                            height={10}
                          />
                        </div>
                        <span>Kim loại kiềm, kiềm thổ, Nhôm.</span>
                      </li>
                      <li className="flex items-start">
                        <div className="w-6 h-6 flex items-center justify-center mr-2">
                          <Image
                            src="/images/homepage/_Dot.svg"
                            alt="dot"
                            width={10}
                            height={10}
                          />
                        </div>
                        <span>Kim loại chuyển tiếp: Fe, Cr, Cu.</span>
                      </li>
                      <li className="flex items-start">
                        <div className="w-6 h-6 flex items-center justify-center mr-2">
                          <Image
                            src="/images/homepage/_Dot.svg"
                            alt="dot"
                            width={10}
                            height={10}
                          />
                        </div>
                        <span>Điện phân: Nóng chảy, dung dịch.</span>
                      </li>
                    </ul>
                    <Button
                      variant="primary"
                      className="sm:hidden px-4 sm:px-5 py-2.5 sm:py-3 text-[14px] sm:text-[16px] shadow-md mt-4"
                      onClick={() =>
                        (window.location.href =
                          "/khoa-hoc/khoa-hoc-livestream-hoa-hoc-lop-12")
                      }
                    >
                      Chinh phục Hóa 12 ngay
                    </Button>
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="w-full py-10 sm:py-16 bg-white">
        <div className="max-w-[1200px] mx-auto px-4 sm:px-4 flex flex-col items-center gap-8 md:gap-12">
          {/* Heading and supporting text */}
          <div className="text-center max-w-3xl">
            <h2 className="text-2xl sm:text-3xl md:text-5xl font-semibold text-[#114721] mb-4 leading-tight md:leading-[1.2]">
              Giải đáp thắc mắc từ A-Z 🔍
            </h2>
            <p className="text-lg text-[#535862]">
              Những câu hỏi &ldquo;kinh điển&rdquo; về khóa học của Ông Ba Dạy
              Hóa
            </p>
          </div>

          {/* FAQ Accordion */}
          <div className="w-full max-w-3xl">
            <div className="flex flex-col gap-4">
              {/* FAQ Item 1 */}
              <div className="border-b border-[#E9EAEB] pb-6">
                <div
                  className="flex justify-between items-start gap-6 cursor-pointer"
                  onClick={() => toggleFaq(0)}
                >
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-[#181D27] mb-2">
                      Ông Ba Dạy Hóa cung cấp những khóa học nào?
                    </h3>
                    {activeFaq === 0 && (
                      <p className="text-base text-[#535862]">
                        Chúng tôi cung cấp 3 khóa học chính dành cho học sinh lớp 10, 11 và 12, bao gồm cả nội dung cơ bản và nâng cao.
                      </p>
                    )}
                  </div>
                  <div className="pt-1">
                    <Image
                      src={
                        activeFaq === 0
                          ? "/images/homepage/chevron-up.svg"
                          : "/images/homepage/chevron-down.svg"
                      }
                      alt="Expand"
                      width={24}
                      height={24}
                      className="transition-transform duration-300"
                    />
                  </div>
                </div>
              </div>

              {/* FAQ Item 2 */}
              <div className="border-b border-[#E9EAEB] pb-6 pt-6">
                <div
                  className="flex justify-between items-start gap-6 cursor-pointer"
                  onClick={() => toggleFaq(1)}
                >
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-[#181D27] mb-2">
                      Hình thức học tập như thế nào?
                    </h3>
                    {activeFaq === 1 && (
                      <p className="text-base text-[#535862]">
                        Khóa học được tổ chức dưới hình thức livestream trên Discord và sau đó video bài giảng sẽ được đăng tải lên website để học sinh xem lại.
                      </p>
                    )}
                  </div>
                  <div className="pt-1">
                    <Image
                      src={
                        activeFaq === 1
                          ? "/images/homepage/chevron-up.svg"
                          : "/images/homepage/chevron-down.svg"
                      }
                      alt="Expand"
                      width={24}
                      height={24}
                      className="transition-transform duration-300"
                    />
                  </div>
                </div>
              </div>

              {/* FAQ Item 3 */}
              <div className="border-b border-[#E9EAEB] pb-6 pt-6">
                <div
                  className="flex justify-between items-start gap-6 cursor-pointer"
                  onClick={() => toggleFaq(2)}
                >
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-[#181D27] mb-2">
                      Tôi có cần kiến thức nền tảng vững không?
                    </h3>
                    {activeFaq === 2 && (
                      <p className="text-base text-[#535862]">
                        Không cần! Khóa học được thiết kế phù hợp với mọi học sinh, kể cả những bạn chưa có nền tảng vững về hóa học.
                      </p>
                    )}
                  </div>
                  <div className="pt-1">
                    <Image
                      src={
                        activeFaq === 2
                          ? "/images/homepage/chevron-up.svg"
                          : "/images/homepage/chevron-down.svg"
                      }
                      alt="Expand"
                      width={24}
                      height={24}
                      className="transition-transform duration-300"
                    />
                  </div>
                </div>
              </div>

              {/* FAQ Item 4 */}
              <div className="border-b border-[#E9EAEB] pb-6 pt-6">
                <div
                  className="flex justify-between items-start gap-6 cursor-pointer"
                  onClick={() => toggleFaq(3)}
                >
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-[#181D27] mb-2">
                      Quy trình đăng ký và thanh toán như thế nào?
                    </h3>
                    {activeFaq === 3 && (
                      <p className="text-base text-[#535862]">
                        Bạn chọn khóa học phù hợp trên website, thanh toán qua cổng Payos (hỗ trợ ZaloPay, MoMo, chuyển khoản), sau đó nhận mã kích hoạt để tham gia kênh học tập trên Discord.
                      </p>
                    )}
                  </div>
                  <div className="pt-1">
                    <Image
                      src={
                        activeFaq === 3
                          ? "/images/homepage/chevron-up.svg"
                          : "/images/homepage/chevron-down.svg"
                      }
                      alt="Expand"
                      width={24}
                      height={24}
                      className="transition-transform duration-300"
                    />
                  </div>
                </div>
              </div>

              {/* FAQ Item 5 */}
              <div className="border-b border-[#E9EAEB] pb-6 pt-6">
                <div
                  className="flex justify-between items-start gap-6 cursor-pointer"
                  onClick={() => toggleFaq(4)}
                >
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-[#181D27] mb-2">
                      Tôi có nhận được tài liệu học tập không?
                    </h3>
                    {activeFaq === 4 && (
                      <p className="text-base text-[#535862]">
                        Có! Bạn sẽ được gửi sách giáo trình miễn phí đến địa chỉ và có thể tải các tài liệu PDF đính kèm với mỗi video bài giảng.
                      </p>
                    )}
                  </div>
                  <div className="pt-1">
                    <Image
                      src={
                        activeFaq === 4
                          ? "/images/homepage/chevron-up.svg"
                          : "/images/homepage/chevron-down.svg"
                      }
                      alt="Expand"
                      width={24}
                      height={24}
                      className="transition-transform duration-300"
                    />
                  </div>
                </div>
              </div>

              {/* FAQ Item 6 */}
              <div className="border-b border-[#E9EAEB] pb-6 pt-6">
                <div
                  className="flex justify-between items-start gap-6 cursor-pointer"
                  onClick={() => toggleFaq(5)}
                >
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-[#181D27] mb-2">
                      Thời hạn truy cập khóa học là bao lâu?
                    </h3>
                    {activeFaq === 5 && (
                      <p className="text-base text-[#535862]">
                        Thời hạn truy cập video bài giảng và tài liệu là 1 năm kể từ ngày đăng ký.
                      </p>
                    )}
                  </div>
                  <div className="pt-1">
                    <Image
                      src={
                        activeFaq === 5
                          ? "/images/homepage/chevron-up.svg"
                          : "/images/homepage/chevron-down.svg"
                      }
                      alt="Expand"
                      width={24}
                      height={24}
                      className="transition-transform duration-300"
                    />
                  </div>
                </div>
              </div>

              {/* FAQ Item 7 */}
              <div className="border-b border-[#E9EAEB] pb-6 pt-6">
                <div
                  className="flex justify-between items-start gap-6 cursor-pointer"
                  onClick={() => toggleFaq(6)}
                >
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-[#181D27] mb-2">
                      Làm thế nào để được hỗ trợ khi gặp khó khăn?
                    </h3>
                    {activeFaq === 6 && (
                      <p className="text-base text-[#535862]">
                        Bạn có thể liên hệ qua số điện thoại/Zalo: 0828949479 hoặc sử dụng kênh hỏi đáp riêng trên Discord nếu đã tham gia khóa học.
                      </p>
                    )}
                  </div>
                  <div className="pt-1">
                    <Image
                      src={
                        activeFaq === 6
                          ? "/images/homepage/chevron-up.svg"
                          : "/images/homepage/chevron-down.svg"
                      }
                      alt="Expand"
                      width={24}
                      height={24}
                      className="transition-transform duration-300"
                    />
                  </div>
                </div>
              </div>

              {/* FAQ Item 8 */}
              <div className="border-b border-[#E9EAEB] pb-6 pt-6">
                <div
                  className="flex justify-between items-start gap-6 cursor-pointer"
                  onClick={() => toggleFaq(7)}
                >
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-[#181D27] mb-2">
                      Có chương trình kiểm tra, đánh giá định kỳ không?
                    </h3>
                    {activeFaq === 7 && (
                      <p className="text-base text-[#535862]">
                        Có! Sau mỗi buổi livestream đều có bài tập, cùng với hệ thống câu hỏi trên website và các sự kiện thi thử, kiểm tra giữa kỳ, cuối kỳ thường xuyên.
                      </p>
                    )}
                  </div>
                  <div className="pt-1">
                    <Image
                      src={
                        activeFaq === 7
                          ? "/images/homepage/chevron-up.svg"
                          : "/images/homepage/chevron-down.svg"
                      }
                      alt="Expand"
                      width={24}
                      height={24}
                      className="transition-transform duration-300"
                    />
                  </div>
                </div>
              </div>

              {/* FAQ Item 9 */}
              <div className="border-b border-[#E9EAEB] pb-6 pt-6">
                <div
                  className="flex justify-between items-start gap-6 cursor-pointer"
                  onClick={() => toggleFaq(8)}
                >
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-[#181D27] mb-2">
                      Ai là người giảng dạy trong khóa học?
                    </h3>
                    {activeFaq === 8 && (
                      <p className="text-base text-[#535862]">
                        Người đứng lớp trực tiếp là Ông Ba Dạy Hóa (Ba) với hơn 13 năm kinh nghiệm đào tạo học sinh cấp 3.
                      </p>
                    )}
                  </div>
                  <div className="pt-1">
                    <Image
                      src={
                        activeFaq === 8
                          ? "/images/homepage/chevron-up.svg"
                          : "/images/homepage/chevron-down.svg"
                      }
                      alt="Expand"
                      width={24}
                      height={24}
                      className="transition-transform duration-300"
                    />
                  </div>
                </div>
              </div>

              {/* FAQ Item 10 */}
              <div className="border-b border-[#E9EAEB] pb-6 pt-6">
                <div
                  className="flex justify-between items-start gap-6 cursor-pointer"
                  onClick={() => toggleFaq(9)}
                >
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-[#181D27] mb-2">
                      Điểm khác biệt của Ông Ba Dạy Hóa so với các trung tâm khác là gì?
                    </h3>
                    {activeFaq === 9 && (
                      <p className="text-base text-[#535862]">
                        Chúng tôi hướng đến giảng dạy bản chất hóa học qua thí nghiệm trực quan, kết hợp AI phân tích quá trình học tập để cung cấp bài tập phù hợp và kiến thức bổ sung cho từng học sinh.
                      </p>
                    )}
                  </div>
                  <div className="pt-1">
                    <Image
                      src={
                        activeFaq === 9
                          ? "/images/homepage/chevron-up.svg"
                          : "/images/homepage/chevron-down.svg"
                      }
                      alt="Expand"
                      width={24}
                      height={24}
                      className="transition-transform duration-300"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* CTA Box */}
          <div className="w-full max-w-[1200px] bg-[#FAFAFA] rounded-2xl border border-[#E9EAEB] p-6 md:p-8 lg:p-10 flex flex-col items-center gap-6 md:gap-8 mt-4 md:mt-6">
            <div className="flex flex-col items-center gap-2">
              <h3 className="text-xl font-semibold text-[#181D27] text-center">
                Vẫn còn thắc mắc?
              </h3>
              <p className="text-lg text-[#535862] text-center">
                Nhận tư vấn với đội ngũ siêu nhiệt tình đấy nhé!
              </p>
            </div>
            <div className="sm:pt-8 md:pt-0">
              <Button variant="primary" className="px-4 sm:px-5 py-2.5 sm:py-3 text-[14px] sm:text-[16px] shadow-md"
                onClick={() => {
                  gaEvent({
                    action: 'click_faq_get_consultation',
                    category: 'Homepage CTA',
                    label: 'FAQ Section - Nhận tư vấn',
                  });
                  window.open('https://zalo.me/0828949479', '_blank');
                }}
              >
                Nhận tư vấn
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      {/* <CtaSection /> */}
    </div>
  );
}
