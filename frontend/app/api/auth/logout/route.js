import { cookies } from "next/headers";
import { NextResponse } from "next/server";

export async function POST() {
  try {
    // Xóa cookie khi đăng xuất
    const cookieStore = await cookies();

    // Xóa các cookie liên quan đến xác thực
    await cookieStore.delete("access_token", { path: "/" });
    await cookieStore.delete("user_data", { path: "/" });

    // Xóa các cookie liên quan đến đơn hàng completed
    await cookieStore.delete("hasCompletedOrder", { path: "/" });
    await cookieStore.delete("completedOrderInfo", { path: "/" });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Lỗi đăng xuất:", error);
    return NextResponse.json(
      { success: false, error: "Đăng xuất thất bại" },
      { status: 500 }
    );
  }
}

// Hỗ trợ GET cho các trường hợp đặc biệt
export async function GET() {
  return await POST();
}
