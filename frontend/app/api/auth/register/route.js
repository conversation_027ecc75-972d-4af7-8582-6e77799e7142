import { cookies } from 'next/headers'
import { NextResponse } from 'next/server'
import strapi from '../../strapi'

export async function POST(request) {
    try {
        // L<PERSON>y dữ liệu từ body
        const { email, password } = await request.json()

        if (!email || !password) {
            return NextResponse.json(
                { success: false, error: 'Email và mật khẩu là bắt buộc' },
                { status: 400 }
            )
        }

        try {
            // Gọi API đăng ký từ Strapi
            const response = await strapi.auth.signup(email, password)

            // Nếu đăng ký thành công
            if (response.jwt) {
                // Tạo cookie
                const cookieStore = cookies()

                // Thiết lập thời gian hết hạn cookie (15 ngày)
                const expiration = new Date()
                expiration.setDate(expiration.getDate() + 15)

                // Lưu token vào cookie
                cookieStore.set('access_token', response.jwt, {
                    expires: expiration,
                    path: '/',
                    httpOnly: false,
                    secure: true,
                    sameSite: 'lax'
                })

                // <PERSON><PERSON><PERSON> thông tin người dùng vào cookie (không bao gồm thông tin nhạy cảm)
                const userData = {
                    id: response.user.id,
                    email: response.user.email,
                    provider: 'local'
                }

                cookieStore.set('user_data', JSON.stringify(userData), {
                    expires: expiration,
                    path: '/',
                    secure: true,
                    sameSite: 'lax'
                })

                // Trả về thành công
                return NextResponse.json({
                    success: true,
                    user: userData
                })
            } else {
                // Trả về lỗi nếu không có jwt
                return NextResponse.json(
                    { success: false, error: 'Đăng ký thất bại - không nhận được JWT' },
                    { status: 400 }
                )
            }
        } catch (strapiError) {
            console.error('Lỗi từ Strapi API:', strapiError)
            console.error('Response data:', strapiError.response?.data)

            // Xử lý lỗi email đã tồn tại
            if (strapiError.response?.data?.error?.message === 'Email or Username are already taken') {
                return NextResponse.json(
                    { success: false, error: 'Email đã được sử dụng' },
                    { status: 400 }
                )
            }

            // Xử lý các trường hợp lỗi khác từ Strapi
            const errorMessage = strapiError.response?.data?.error?.message ||
                strapiError.message ||
                'Đăng ký thất bại. Vui lòng thử lại sau.'

            return NextResponse.json(
                { success: false, error: errorMessage },
                { status: strapiError.response?.status || 500 }
            )
        }
    } catch (error) {
        console.error('Lỗi tổng thể đăng ký:', error)

        return NextResponse.json(
            { success: false, error: 'Đăng ký thất bại. Vui lòng thử lại sau.' },
            { status: 500 }
        )
    }
} 