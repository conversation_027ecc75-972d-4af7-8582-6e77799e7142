import { NextResponse } from 'next/server'
import strapi from '../../strapi'

export async function GET(request) {
    try {
        // Lấy email từ query params
        const { searchParams } = new URL(request.url)
        const email = searchParams.get('email')

        if (!email) {
            return NextResponse.json(
                { exists: false, error: '<PERSON>ail là bắt buộc' },
                { status: 400 }
            )
        }

        // Gọi API kiểm tra email từ Strapi
        const response = await strapi.auth.findUserByEmail(email)

        // Trả về kết quả kiểm tra
        if (response && response.user) {
            return NextResponse.json({
                exists: true,
                user: {
                    id: response.user.id,
                    email: response.user.email,
                    fullname: response.user.fullname || ''
                }
            })
        }

        // Trả về kết quả nếu email không tồn tại
        return NextResponse.json(
            { exists: false, error: '<PERSON><PERSON> không tồn tại trong hệ thống' },
            { status: 404 }
        )
    } catch (error) {
        console.error('Lỗi kiểm tra email:', error)
        return NextResponse.json(
            { exists: false, error: '<PERSON>hông thể kiểm tra email. Vui lòng thử lại sau.' },
            { status: 500 }
        )
    }
} 