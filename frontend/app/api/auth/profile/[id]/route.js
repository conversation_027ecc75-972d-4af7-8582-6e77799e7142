import { cookies } from "next/headers";
import { NextResponse } from "next/server";
import strapi from "../../../strapi";

export async function GET(request, { params }) {
  try {
    const { id } = params;

    // Lấy token từ cookie để xác thực với Strapi
    const cookieStore = await cookies();
    const token = cookieStore.get("access_token")?.value;

    if (!token) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Gọi API của Strapi để lấy thông tin user
    const response = await strapi.findOne("users", id, {
      populate: ["image"], // Thêm populate để lấy avatar
    });

    // Xử lý dữ liệu trả về
    if (response) {
      // Lấy các thông tin cần thiết
      const userData = {
        id: response.id,
        email: response.email,
        fullname: response.fullname,
        phone: response.phone,
        image: response.image,
        gender: response.gender,
      };

      return NextResponse.json({
        success: true,
        user: userData,
      });
    }

    return NextResponse.json(
      { success: false, error: "User not found" },
      { status: 404 }
    );
  } catch (error) {
    console.error("Error fetching user profile:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch user profile" },
      { status: 500 }
    );
  }
}

export async function PUT(request, { params }) {
  try {
    const id = params.id;
    const data = await request.json();

    // Lấy token từ cookie
    const cookieStore = await cookies();
    const token = cookieStore.get("access_token")?.value;

    if (!token) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Gọi API của Strapi để cập nhật thông tin user bằng hàm updateProfile
    const response = await strapi.auth.updateProfile(id, data, token);

    if (response && response.user) {
      // Cập nhật cookie user_data
      const userData = {
        id: response.user.id,
        email: response.user.email,
        fullname: response.user.fullname,
        phone: response.user.phone,
        image: response.user.image,
        gender: response.user.gender,
      };

      cookieStore.set("user_data", JSON.stringify(userData), {
        path: "/",
        secure: process.env.NODE_ENV === "production",
        sameSite: "strict",
      });

      return NextResponse.json({
        success: true,
        user: userData,
      });
    }

    return NextResponse.json(
      { success: false, error: "Failed to update user" },
      { status: 400 }
    );
  } catch (error) {
    console.error("Error updating user profile:", error);
    return NextResponse.json(
      { success: false, error: "Failed to update user profile" },
      { status: 500 }
    );
  }
}
