import { cookies } from 'next/headers'
import { NextResponse } from 'next/server'
import strapi from '../../strapi'

export async function POST(request) {
    try {
        // L<PERSON>y dữ liệu từ body
        const { email, password, provider, provider_id, googleToken } = await request.json()

        let response

        // X<PERSON><PERSON> thực với Strapi dựa vào provider
        if (provider === 'google') {
            response = await strapi.auth.googleAuth({
                email,
                sub: provider_id,
                credential: googleToken,

            });
        } else if (provider === 'local') { // Thêm điều kiện rõ ràng
            response = await strapi.auth.login(email, password);

        } else {
            throw new Error('Invalid provider');
        }

        // Nếu đăng nhập thành công
        if (response.jwt) {
            // Kiểm tra xem người dùng đã có đơn hàng completed chưa



            // Tạo cookie
            const cookieStore = cookies()

            // Thiết lập thời gian hết hạn cookie (15 ngày hoặc 30 ngày)
            const expiration = new Date()
            expiration.setDate(expiration.getDate() + (provider === 'google' ? 30 : 15))



            // Lưu thêm một phiên bản token có thể truy cập từ client
            cookieStore.set('access_token', response.jwt, {
                expires: expiration,
                path: '/',
                httpOnly: false,
                secure: true,
                sameSite: 'lax',
            })

            // Lưu thông tin người dùng vào cookie (không bao gồm thông tin nhạy cảm)
            const userData = {
                id: response.user.id,
                email: response.user.email,
                fullname: response.user.fullname,
                phone: response.user.phone,
                gender: response.user.gender,
                date: response.user.date,
                image: response.user.image,
                verified_otp: response.user.verified_otp,
                isUploadVideo: response.user.isUploadVideo,
                provider: provider
            }

            cookieStore.set('user_data', JSON.stringify(userData), {
                expires: expiration,
                path: '/',
                secure: true,
                sameSite: 'lax'
            })
            let orderCheckResult = {
                hasCompletedOrder: false,
                latestOrder: null
            };

            try {
                // Truyền token vào hàm checkUserHasCompletedOrder để đảm bảo API này được xác thực
                // Không sử dụng token từ cookie mà truyền trực tiếp từ response
                orderCheckResult = await strapi.auth.checkUserHasCompletedOrder(response.user.id, response.jwt);


                // Lưu trạng thái đơn hàng đã hoàn thành vào cookie nếu có
                if (orderCheckResult.hasCompletedOrder) {
                    cookieStore.set('hasCompletedOrder', 'true', {
                        expires: expiration,
                        path: '/',
                        secure: true,
                        sameSite: 'lax'
                    })

                    // Lưu thông tin đơn hàng gần nhất đã hoàn thành 
                    cookieStore.set('completedOrderInfo', JSON.stringify(orderCheckResult.latestOrder), {
                        expires: expiration,
                        path: '/',
                        secure: true,
                        sameSite: 'lax'
                    })
                }
            } catch (checkOrderError) {
                console.error("Lỗi khi kiểm tra đơn hàng:", checkOrderError);
                // Tiếp tục xử lý, không dừng luồng đăng nhập nếu kiểm tra đơn hàng thất bại
            }


            // Trả về thành công và token để client lưu vào localStorage
            return NextResponse.json({
                success: true,
                user: userData,
                auth_token: response.jwt,
                hasCompletedOrder: orderCheckResult.hasCompletedOrder,
                completedOrderInfo: orderCheckResult.hasCompletedOrder ? orderCheckResult.latestOrder : null
            })
        }

        // Trả về lỗi nếu không có jwt
        return NextResponse.json({ success: false, error: 'Đăng nhập thất bại' }, { status: 401 })
    } catch (error) {
        console.error('Lỗi đăng nhập:', error)
        return NextResponse.json(
            { success: false, error: 'Đăng nhập thất bại. Vui lòng thử lại sau.' },
            { status: 500 }
        )
    }
} 