"use client";

import React, { useState, useEffect } from "react";
import Image from "next/image";
import Link from "next/link";
import slugify from "slugify";
import Button from "../../components/Button";
import strapi from "../api/strapi";
import { getStrapiMedia } from "../../utils/media";

const BlogPage = () => {
  const [activeTab, setActiveTab] = useState("moi-nhat");
  const [blogPosts, setBlogPosts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const tabs = [
    { id: "moi-nhat", label: "Mới nhất" },
    { id: "ly-thuyet", label: "Lý thuyết" },
    { id: "meo-giai-de", label: "Mẹo giải đề" },
    { id: "thi-nghiem", label: "<PERSON>h<PERSON> nghiệ<PERSON>" },
  ];

  // <PERSON>tch posts khi component được mount
  useEffect(() => {
    const fetchPosts = async () => {
      try {
        setLoading(true);
        let response;

        if (activeTab === "moi-nhat") {
          response = await strapi.blog.getAllPosts();
        } else {
          const categoryMap = {
            "ly-thuyet": "Lý thuyết",
            "meo-giai-de": "Mẹo giải đề",
            "thi-nghiem": "Thí nghiệm",
          };
          response = await strapi.blog.getPostsByCategory(
            categoryMap[activeTab]
          );
        }

        if (response && response.data) {
          const formattedPosts = response.data.map((post) => {
            return {
              id: post?.id || "",
              slug: post?.slug || "",
              title: post?.title || "",
              description: post?.description || "",
              category: post?.category || "",
              readTime: post?.readTime || "",
              displayDate: post?.displayDate || "",
              date: post?.date || "",
              image: post?.featuredImage && Array.isArray(post.featuredImage) && post.featuredImage.length > 0 && post.featuredImage[0]?.url
                ? getStrapiMedia(post.featuredImage[0].url)
                : "",
            };
          });

          setBlogPosts(formattedPosts);
        }
      } catch (err) {
        console.error("Error fetching blog posts:", err);
        setError("Không thể tải bài viết. Vui lòng thử lại sau.");
      } finally {
        setLoading(false);
      }
    };

    fetchPosts();
  }, [activeTab]);

  const MobileCardSmall = ({ post }) => (
    <Link href={`/bai-viet/${post.slug}`} className="block w-full">
      <div className="w-full flex gap-4 hover:bg-gray-50 rounded-lg p-2 transition-colors">
        <div className="flex-1 flex flex-col gap-2">
          <span className="text-sm text-[#198C43] font-semibold">
            {post.category}
          </span>
          <h3 className="text-lg font-semibold text-[#181D27] line-clamp-2">
            {post.title}
          </h3>
          <p className="hidden sm:block text-sm text-[#535862] font-normal line-clamp-2">
            {post.description}
          </p>
          <div className="flex items-center gap-2 text-xs text-[#717680]">
            <span>{post.displayDate}</span>
            <span>•</span>
            <span>{post.readTime}</span>
          </div>
        </div>
        <div className="relative w-[104px] h-[69px] sm:w-[201px] sm:h-[134px] rounded-lg overflow-hidden flex-shrink-0">
          <Image
            src={post.image}
            alt={post.title}
            fill
            className="object-cover"
          />
        </div>
      </div>
    </Link>
  );

  const BlogCard = ({ post, isNewest = false }) => (
    <Link href={`/bai-viet/${post.slug}`} className="block w-full">
      <div className="w-full hover:opacity-95 transition-opacity">
        <div className="relative w-full md:h-[252.444px] sm:h-[409px] h-[229px] rounded-lg overflow-hidden">
          <Image
            src={post.image}
            alt={post.title}
            fill
            className="object-cover"
          />
        </div>
        <div className="mt-[20px] flex flex-col gap-1">
          <span
            className={`text-sm font-semibold ${
              isNewest ? "text-[#DD2590]" : "text-[#198C43]"
            }`}
          >
            {post.category}
          </span>
          <h3 className="text-2xl font-semibold text-[#181D27] ">
            {post.title}
          </h3>
          <p className="text-base text-[#535862] font-normal line-clamp-2">
            {post.description}
          </p>
          <div className="flex items-center gap-2 text-xs leading-[18px] text-[#717680]">
            <span>{post.displayDate}</span>
            <span>•</span>
            <span>{post.readTime}</span>
          </div>
        </div>
      </div>
    </Link>
  );

  const FeaturedPost = ({ post }) => (
    <Link href={`/bai-viet/${post.slug}`} className="block w-full">
      <div className="flex flex-col md:flex-row w-full mb-8 gap-8 hover:opacity-95 transition-opacity">
        <div className="relative w-full md:w-[584px] md:h-[289.109px] sm:h-[409px] h-[229px] rounded-lg overflow-hidden">
          <Image
            src={post.image}
            alt={post.title}
            fill
            className="object-cover"
          />
        </div>
        <div className="flex flex-col gap-2">
          <span className="text-sm text-[#DD2590] font-semibold">
            {post.category}
          </span>
          <h2 className="text-3xl leading-[38px] font-semibold text-[#181D27] ">
            {post.title}
          </h2>
          <p className="text-base font-normal text-[#535862] ">
            {post.description}
          </p>
          <div className="flex items-center gap-2 text-xs leading-[18px] text-[#717680]">
            <span>{post.displayDate}</span>
            <span>•</span>
            <span>{post.readTime}</span>
          </div>
        </div>
      </div>
    </Link>
  );

  // Hiển thị loading hoặc error
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#DD2590]"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="flex flex-col items-center">
          <h2 className="text-2xl font-bold text-[#1D2939] mb-4 text-center">{error}</h2>
          <div className="flex justify-center">
            <Button onClick={() => window.location.reload()}>
              Tải lại trang
            </Button>
          </div>
        </div>
      </div>
    );
  }

  const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';

  let itemListJsonLd = null;
  if (blogPosts && blogPosts.length > 0) {
    itemListJsonLd = {
      '@context': 'https://schema.org',
      '@type': 'ItemList',
      name: 'Danh sách bài viết Hóa học',
      description: 'Các bài viết mới nhất về Hóa học từ Ông Ba Dạy Hóa.',
      url: `${siteUrl}/bai-viet`, // URL của trang danh sách này
      itemListElement: blogPosts.map((post, index) => ({
        '@type': 'ListItem',
        position: index + 1,
        item: {
          '@type': 'Article', // Hoặc 'WebPage' nếu không muốn chi tiết như Article
          '@id': `${siteUrl}/bai-viet/${post.slug}`,
          name: post.title,
          url: `${siteUrl}/bai-viet/${post.slug}`,
          // Tùy chọn thêm:
          // image: post.image ? `${post.image}` : undefined, // Giả sử post.image là URL tuyệt đối
          // datePublished: post.date ? new Date(post.date).toISOString() : undefined,
          // description: post.description ? post.description.substring(0,100) : undefined,
        },
      })),
    };
  }

  return (
    <>
      {itemListJsonLd && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(itemListJsonLd) }}
        />
      )}
      <div className="min-h-screen bg-[#FFFFFF]">
        <div className="max-w-[1200px] mx-auto px-4 py-12">
          <h1 className="text-3xl font-bold text-center text-[#1D2939] mb-12">
          Blog kiến thức về Hoá học
        </h1>

        {/* Tabs */}
        <div className="w-full overflow-x-auto scrollbar-hide mb-8">
          <div className="inline-flex min-w-fit border border-[#EAECF0] rounded-lg bg-[#FAFAFA]">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`whitespace-nowrap px-4 py-2 text-base font-semibold ${
                  activeTab === tab.id
                    ? "text-[#414651] border border-[#E9EAEB] rounded-lg bg-[#FFFFFF]"
                    : "text-[#717680] bg-[#FAFAFA]"
                }`}
              >
                {tab.label}
              </button>
            ))}
          </div>
        </div>

        {/* Blog Posts Section */}
        <div className="w-full">
          {blogPosts.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-lg text-[#717680]">
                Không có bài viết nào trong danh mục này.
              </p>
            </div>
          ) : (
            <>
              {/* Mobile/Tablet View */}
              <div className="block lg:hidden">
                {/* First 4 posts */}
                <div className="flex flex-col gap-8 mb-8">
                  {blogPosts.slice(0, 4).map((post, index) => (
                    <BlogCard
                      key={post.id}
                      post={post}
                      isNewest={index === 0}
                    />
                  ))}
                </div>

                {/* Remaining posts in small card format */}
                <div className="flex flex-col gap-4">
                  {blogPosts.slice(4).map((post) => (
                    <MobileCardSmall key={post.id} post={post} />
                  ))}
                </div>
              </div>

              {/* Desktop View */}
              <div className="hidden lg:block">
                {/* Featured Post */}
                {blogPosts[0] && <FeaturedPost post={blogPosts[0]} />}

                {/* Blog Grid */}
                <div className="grid grid-cols-3 gap-8">
                  {blogPosts.slice(1).map((post) => (
                    <BlogCard key={post.id} post={post} />
                  ))}
                </div>
              </div>
            </>
          )}
        </div>

        {/* Load More Button */}
        {blogPosts.length > 0 && (
          <div className="flex justify-center mt-12">
            <Button variant="secondaryColor">Xem thêm</Button>
          </div>
        )}
      </div>
    </div>
    </>
  );
};

export default BlogPage;
