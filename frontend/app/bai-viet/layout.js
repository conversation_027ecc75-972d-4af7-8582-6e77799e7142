// frontend/app/bai-viet/layout.js

const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';
const ogImageUrl = `${siteUrl}/images/metadata-img/blog-og.jpg`; // Hoặc một ảnh chung cho trang blog

export const metadata = {
  title: 'Blog Kiến Thức Hóa Học | Ông Ba Dạy Hóa',
  description: 'Cập nhật những bài viết mới nhất về lý thuyết Hóa học, mẹo gi<PERSON>i đề, thí nghiệm thú vị và các chủ đề liên quan đến Hóa học từ Ông Ba Dạy Hóa.',
  alternates: {
    canonical: `${siteUrl}/bai-viet`,
  },
  openGraph: {
    title: 'Blog Kiến Thức Hóa Học | Ông Ba Dạy Hóa',
    description: 'C<PERSON><PERSON> nhật những bài viết mới nhất về lý thuyết Hóa học, mẹo giải đề, thí nghiệm thú vị...',
    url: `${siteUrl}/bai-viet`,
    siteName: 'Ông Ba Dạy Hóa',
    images: [
      {
        url: ogImageUrl,
        width: 1200,
        height: 630,
        alt: 'Blog Ông Ba Dạy Hóa',
      },
    ],
    locale: 'vi_VN',
    type: 'website', // Hoặc 'blog' nếu phù hợp hơn
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Blog Kiến Thức Hóa Học | Ông Ba Dạy Hóa',
    description: 'Cập nhật những bài viết mới nhất về lý thuyết Hóa học...',
    images: [ogImageUrl],
  },
};

export default function BlogListingLayout({ children }) {
  return <>{children}</>;
}