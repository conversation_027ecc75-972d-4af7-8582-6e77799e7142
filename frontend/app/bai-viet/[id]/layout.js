// frontend/app/bai-viet/[id]/layout.js
import strapi from "../../api/strapi"; // Đảm bảo đường dẫn này chính xác

// Hàm fetch dữ liệu bài viết (cần điều chỉnh để phù hợp với API client của bạn)
async function getPostData(slug) {
  try {
    // Giả sử params.id từ URL chính là slug của bài viết
    const post = await strapi.blog.getPostBySlug(slug); // API call thực tế
    if (post && post.title && post.description) { // Kiểm tra các trường cần thiết
        return {
            title: post.title,
            description: post.description,
            slug: post.slug, // Cần slug để tạo canonical URL
            date: post.date || post.publishedAt, // Hoặc trường ngày phù hợp
            updatedAt: post.updatedAt,
            // featuredImage: post.featuredImage?.[0]?.url // Nếu có và muốn dùng cho OG image
        };
    }
    console.warn(`Post data not found or incomplete for slug: ${slug}`);
    return null;
  } catch (error) {
    console.error(`Error fetching post data for slug ${slug}:`, error);
    return null;
  }
}

export async function generateMetadata({ params }) {
  const postSlug = params.id; // 'id' ở đây thực chất là slug từ URL
  const post = await getPostData(postSlug);

  const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';
  const defaultOgImage = `${siteUrl}/images/metadata-img/blog-og.jpg`;

  if (!post) {
    return {
      title: 'Không Tìm Thấy Bài Viết',
      description: 'Bài viết bạn tìm kiếm không tồn tại hoặc đã bị xóa.',
      alternates: {
        canonical: `${siteUrl}/bai-viet`,
      },
      openGraph: {
        title: 'Không Tìm Thấy Bài Viết',
        description: 'Bài viết bạn tìm kiếm không tồn tại hoặc đã bị xóa.',
        images: [{ url: defaultOgImage }],
        url: `${siteUrl}/bai-viet`,
      },
      twitter: {
        card: 'summary_large_image',
        title: 'Không Tìm Thấy Bài Viết',
        description: 'Bài viết bạn tìm kiếm không tồn tại hoặc đã bị xóa.',
        images: [defaultOgImage],
      },
    };
  }

  // const postOgImage = post.featuredImage ? `${process.env.NEXT_PUBLIC_STRAPI_URL}${post.featuredImage}` : defaultOgImage;
  // Nếu featuredImage là URL tương đối từ Strapi, cần NEXT_PUBLIC_STRAPI_URL
  // Nếu featuredImage đã là URL đầy đủ từ getStrapiMedia, thì không cần nối thêm.
  // Tạm thời dùng defaultOgImage
   const postOgImage = defaultOgImage;


  return {
    title: `${post.title} | Ông Ba Dạy Hóa`,
    description: post.description.substring(0, 160), // Giới hạn độ dài description
    alternates: {
      canonical: `${siteUrl}/bai-viet/${post.slug}`,
    },
    openGraph: {
      title: post.title,
      description: post.description.substring(0, 160),
      url: `${siteUrl}/bai-viet/${post.slug}`,
      siteName: 'Ông Ba Dạy Hóa',
      images: [
        {
          url: postOgImage,
          width: 1200,
          height: 630,
          alt: post.title,
        },
      ],
      type: 'article',
      publishedTime: post.date ? new Date(post.date).toISOString() : undefined,
      modifiedTime: post.updatedAt ? new Date(post.updatedAt).toISOString() : undefined,
      authors: ['Ông Ba Dạy Hóa'],
    },
    twitter: {
      card: 'summary_large_image',
      title: post.title,
      description: post.description.substring(0, 160),
      images: [postOgImage],
    },
  };
}

export default function PostLayout({ children }) {
  return <>{children}</>;
}