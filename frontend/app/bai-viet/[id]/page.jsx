"use client";

import React, { useState, useEffect } from "react";
import Image from "next/image";
import Link from "next/link";
import strapi from "../../api/strapi";
import { useParams } from "next/navigation";
import { getStrapiMedia } from "../../../utils/media";
import Button from "../../../components/Button";

export default function BlogDetail() {
  const params = useParams();
  const [post, setPost] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchBlogPost = async () => {
      try {
        setLoading(true);
        const response = await strapi.blog.getPostBySlug(params.id);

        if (!response) {
          setError("Không tìm thấy bài viết");
          return;
        }

        const postData = response;

        // Format post data
        const formattedPost = {
          id: postData.id,
          slug: postData.slug || "",
          title: postData.title || "",
          description: postData.description || "",
          category: postData.category || "",

          readTime: postData.readTime || "5 phút đọc",
          displayDate:
            postData.displayDate ||
            new Date(postData.date).toLocaleDateString("vi-VN"),
          date: postData.date,
          image: postData.featuredImage?.[0]?.url
            ? getStrapiMedia(postData.featuredImage[0].url)
            : "",
          sections: postData.sections
            ? postData.sections
                .map((section) => ({
                  id: section.id,
                  title: section.title,
                  content: section.content.map(
                    (contentItem) => contentItem.children[0].text
                  ), // Thay đổi ở đây
                  image: section.image?.[0]?.url
                    ? getStrapiMedia(section.image[0].url)
                    : null,
                  imageCaption: section.imageCaption || "",
                  order: section.order,
                }))
                .sort((a, b) => a.order - b.order)
            : [],
        };

        setPost(formattedPost);
      } catch (err) {
        console.error("Error fetching blog post:", err);
        setError("Không thể tải bài viết. Vui lòng thử lại sau.");
      } finally {
        setLoading(false);
      }
    };

    if (params?.id) {
      fetchBlogPost();
    }
  }, [params?.id]);

  // Hiển thị trạng thái loading
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#DD2590]"></div>
      </div>
    );
  }

  // Hiển thị thông báo lỗi
  if (error || !post) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="flex flex-col items-center">
          <h1 className="text-2xl font-bold text-[#1D2939] mb-4 text-center">
            {error || "Không tìm thấy bài viết"}
          </h1>
          <Link href="/bai-viet">
            <Button>Quay lại trang Blog</Button>
          </Link>
        </div>
      </div>
    );
  }

  const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';

  // Đảm bảo post đã được fetch và không phải là null/undefined trước khi tạo JSON-LD
  let articleJsonLd = null;
  if (post && post.slug) { // Thêm kiểm tra post.slug để đảm bảo URL hợp lệ
    articleJsonLd = {
      '@context': 'https://schema.org',
      '@type': 'Article',
      mainEntityOfPage: {
        '@type': 'WebPage',
        '@id': `${siteUrl}/bai-viet/${post.slug}`,
      },
      headline: post.title,
      description: post.description ? post.description.substring(0, 250) : '', // Giới hạn và kiểm tra
      image: post.image ? [`${post.image}`] : [`${siteUrl}/images/metadata-img/blog-og.jpg`], // Giả sử post.image đã là URL tuyệt đối từ getStrapiMedia
      author: {
        '@type': 'Organization', // Hoặc Person nếu bạn muốn
        name: 'Ông Ba Dạy Hóa',
        url: siteUrl, // URL của tổ chức/tác giả
      },
      publisher: {
        '@type': 'Organization',
        name: 'Ông Ba Dạy Hóa',
        logo: {
          '@type': 'ImageObject',
          url: `${siteUrl}/images/Logo.png`, // Đảm bảo Logo.png có trong public/images
        },
      },
      datePublished: post.date ? new Date(post.date).toISOString() : undefined,
      dateModified: post.updatedAt ? new Date(post.updatedAt).toISOString() : (post.date ? new Date(post.date).toISOString() : undefined),
    };
  }

  return (
    <>
      {articleJsonLd && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(articleJsonLd) }}
        />
      )}
      <div className="min-h-screen bg-white">
        <div className="max-w-[1200px] mx-auto px-4 py-12">
          {/* Breadcrumb */}
        <div className="flex items-center gap-2 text-sm ">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="20"
            height="20"
            viewBox="0 0 20 20"
            fill="none"
          >
            <path
              d="M6.66667 14.1667H13.3333M9.18141 2.30333L3.52949 6.69927C3.15168 6.99312 2.96278 7.14005 2.82669 7.32405C2.70614 7.48704 2.61633 7.67065 2.56169 7.86588C2.5 8.08627 2.5 8.32558 2.5 8.80421V14.8333C2.5 15.7667 2.5 16.2335 2.68166 16.59C2.84144 16.9036 3.09641 17.1585 3.41002 17.3183C3.76654 17.5 4.23325 17.5 5.16667 17.5H14.8333C15.7668 17.5 16.2335 17.5 16.59 17.3183C16.9036 17.1585 17.1586 16.9036 17.3183 16.59C17.5 16.2335 17.5 15.7667 17.5 14.8333V8.80421C17.5 8.32558 17.5 8.08627 17.4383 7.86588C17.3837 7.67065 17.2939 7.48704 17.1733 7.32405C17.0372 7.14005 16.8483 6.99312 16.4705 6.69927L10.8186 2.30333C10.5258 2.07562 10.3794 1.96177 10.2178 1.918C10.0752 1.87938 9.92484 1.87938 9.78221 1.918C9.62057 1.96177 9.47418 2.07562 9.18141 2.30333Z"
              stroke="#717680"
              strokeWidth="1.66667"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 16 16"
            fill="none"
          >
            <path
              d="M6 12L10 8L6 4"
              stroke="#D5D7DA"
              strokeWidth="1.33333"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
          <Link
            href="/bai-viet"
            className="text-sm text-[#535862] font-semibold"
          >
            Blog
          </Link>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 16 16"
            fill="none"
          >
            <path
              d="M6 12L10 8L6 4"
              stroke="#D5D7DA"
              strokeWidth="1.33333"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
          <span className="text-sm text-[#198C43] font-semibold">
            {post.title}
          </span>
        </div>

        {/* Featured Image */}
        <div className="relative w-full h-[114.333px] sm:h-[203px] md:h-[400px] rounded-xl overflow-hidden md:my-12 my-8">
          <Image
            src={post.image}
            alt={post.title}
            fill
            className="object-cover"
          />
        </div>

        <div className="max-w-[792px] mx-auto justify-center items-center">
          {/* Article Header */}
          <div className="mb-6">
            <span className="text-sm text-[#198C43] font-semibold mb-2 block">
              {post.category}
            </span>
            <div className="flex items-center gap-2 text-sm text-[#717680] mb-2">
              <span>{post.displayDate}</span>
              <span>•</span>
              <span>{post.readTime}</span>
            </div>
            <h1 className="text-5xl leading-[60px] tracking-[-0.96px] font-semibold text-[#181D27]">
              {post.title}
            </h1>
          </div>

          {/* Article Description */}
          {post.description && (
            <div className="mb-12">
              <p className="text-lg text-[#414651] font-medium">
                {post.description}
              </p>
            </div>
          )}

          {/* Article Content */}
          <div className="prose prose-lg ">
            {post.sections && post.sections.length > 0 ? (
              post.sections.map((section) => (
                <div key={section.id} className="mb-8">
                  {section.title && <h2>{section.title}</h2>}
                  {section.content && section.content.length > 0 ? (
                    section.content.map((paragraph, index) => (
                      <p key={index} className="">
                        {paragraph}
                      </p>
                    ))
                  ) : (
                    <p className="text-center text-[#717680]">
                      Nội dung không có sẵn.
                    </p>
                  )}
                  {section.image && (
                    <div className="my-6">
                      <div className="relative w-full h-[257.25px] sm:h-[456.75px]  md:h-[480px] rounded-lg overflow-hidden">
                        <Image
                          src={section.image}
                          alt={section.title || post.title}
                          fill
                          className="object-cover"
                        />
                      </div>
                      {section.imageCaption && (
                        <p className="text-sm text-[#535862] mt-2">
                          {section.imageCaption}
                        </p>
                      )}
                    </div>
                  )}
                </div>
              ))
            ) : (
              <p className="text-center text-[#717680]">
                Nội dung đang được cập nhật...
              </p>
            )}
          </div>
        </div>

        {/* Related Posts - Sẽ thêm sau nếu cần */}
      </div>
    </div>
    </>
  );
}
