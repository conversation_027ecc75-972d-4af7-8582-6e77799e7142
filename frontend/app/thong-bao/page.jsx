"use client";

import React from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import Button from "../../components/Button";
const NotificationsPage = () => {
  const router = useRouter();

  return (
    <div className="h-screen bg-[#FFFFFF] flex flex-col">
      {/* Header */}
      <div className="bg-[#299D55] text-[#FFFFFF] px-4 py-2 flex items-center gap-2">
        <button onClick={() => router.back()} className="">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
          >
            <path
              d="M19 12H5M5 12L12 19M5 12L12 5"
              stroke="white"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </button>
        <h1 className="text-lg font-semibold flex-1 text-center py-2.5">
          Thông báo
        </h1>
      </div>

      {/* Empty State */}
      <div className="flex-1 flex flex-col gap-4 items-center justify-center px-4 py-8">
        <div className="bg-[#F5F5F5] rounded-full p-8 ">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="128"
            height="160"
            viewBox="0 0 128 160"
            fill="none"
          >
            <path
              opacity="0.27"
              d="M43.7397 22.5L1.22175 139.73C0.601667 141.436 0.662232 143.315 1.39088 144.978C2.11954 146.641 3.46034 147.959 5.13514 148.66C14.996 152.799 36.1529 160.002 64.0395 160.002C91.9261 160.002 113.077 152.802 122.946 148.66C124.62 147.959 125.961 146.64 126.689 144.978C127.417 143.315 127.478 141.436 126.858 139.73L84.3397 22.5H43.7397Z"
              fill="#FF97C9"
            />
            <path
              d="M19.1859 36.0986C21.3725 36.0986 23.1452 34.325 23.1452 32.1372C23.1452 29.9494 21.3725 28.1758 19.1859 28.1758C16.9992 28.1758 15.2266 29.9494 15.2266 32.1372C15.2266 34.325 16.9992 36.0986 19.1859 36.0986Z"
              fill="#738DE0"
            />
            <path
              d="M108.897 36.0986C111.083 36.0986 112.856 34.325 112.856 32.1372C112.856 29.9494 111.083 28.1758 108.897 28.1758C106.71 28.1758 104.938 29.9494 104.938 32.1372C104.938 34.325 106.71 36.0986 108.897 36.0986Z"
              fill="#738DE0"
            />
            <path
              d="M64.0374 42.6025C66.2241 42.6025 67.9967 40.8289 67.9967 38.6411C67.9967 36.4533 66.2241 34.6797 64.0374 34.6797C61.8508 34.6797 60.0781 36.4533 60.0781 38.6411C60.0781 40.8289 61.8508 42.6025 64.0374 42.6025Z"
              fill="#738DE0"
            />
            <path
              d="M117.068 24.4564C117.068 31.8599 95.5753 37.8599 63.8132 37.8599C32.0511 37.8599 11.0156 31.8596 11.0156 24.4564C11.0156 17.0533 32.0407 11.0547 63.8048 11.0547C95.569 11.0547 117.068 17.0547 117.068 24.4564Z"
              fill="#99ADF9"
            />
            <g style={{ mixBlendMode: "soft-light", opacity: "0.42" }}>
              <path
                d="M117.075 19.5636C117.075 26.965 95.5826 32.9671 63.8205 32.9671C32.0584 32.9671 11.0312 26.965 11.0312 19.5636C11.0312 12.1622 32.0563 6.16016 63.8205 6.16016C95.5846 6.16016 117.075 12.1622 117.075 19.5636Z"
                fill="white"
              />
            </g>
            <path
              d="M64.0929 24.0637C72.5287 24.1097 80.9371 23.0976 89.121 21.0511C87.5037 9.18261 76.4577 0 63.0706 0C49.8856 0 38.9715 8.90957 37.1016 20.5217C44.9704 22.7694 54.2094 24.0637 64.0929 24.0637Z"
              fill="#99ADF9"
            />
            <path
              d="M84.6082 115.034C84.8124 113.065 82.9404 112.109 79.7463 111.882C76.5522 111.654 74.6193 112.336 74.4152 114.304C74.3422 114.985 74.5095 115.67 74.8882 116.24C74.9577 116.349 74.9946 116.476 74.9946 116.605C74.9946 116.735 74.9577 116.861 74.8882 116.97C74.587 117.458 74.3522 117.962 74.524 118.155C74.772 118.433 75.3588 118.19 76.0315 117.729C76.1264 117.665 76.2363 117.626 76.3506 117.617C76.4648 117.607 76.5796 117.627 76.684 117.674C77.1324 117.867 77.601 118.008 78.0809 118.096C78.2139 118.12 78.3369 118.183 78.4343 118.277C78.5317 118.37 78.5992 118.491 78.6284 118.623C78.7428 119.196 78.9699 119.768 79.4221 119.805C79.9946 119.859 80.2917 119.397 80.403 118.719C80.4236 118.576 80.4891 118.444 80.59 118.341C80.6908 118.238 80.8218 118.17 80.964 118.146C81.2104 118.108 81.4543 118.055 81.6945 117.988C81.809 117.958 81.9292 117.96 82.043 117.991C82.1569 118.023 82.2604 118.084 82.3432 118.169C82.8889 118.701 83.3946 118.986 83.6903 118.742C83.8813 118.579 83.7647 118.073 83.5797 117.596C83.5303 117.473 83.5174 117.339 83.5424 117.209C83.5675 117.079 83.6294 116.959 83.7209 116.863C84.2194 116.374 84.533 115.728 84.6082 115.034Z"
              fill="#FF97C9"
            />
            <path
              d="M42.6578 97.8752C40.4523 102.839 39.9865 112.674 39.6603 113.328C39.334 113.983 37.0603 115.162 37.6415 116.359C38.1757 117.458 41.8794 118.957 42.9841 118.649C44.7013 118.184 46.3976 113.602 47.4415 110.7C51.2575 112.991 57.6383 113.766 57.6383 113.766C57.6383 113.766 56.9907 118.527 56.4933 119.57C55.9959 120.612 54.2533 121.886 54.6119 123.157C54.9705 124.428 58.4091 125.146 59.9354 125.343C61.4616 125.539 63.933 117.157 64.5037 114.222C74.6568 115.297 86.9124 114.516 86.9124 114.516C86.9124 114.516 88.3799 119.635 86.9448 121.918C86.2902 122.961 85.8745 125.163 86.2272 125.31C88.4119 126.19 91.2283 125.693 92.127 125.048C93.1382 124.331 96.6912 107.813 94.5389 101.579C92.3865 95.3437 88.1354 91.7698 80.6536 89.8797C73.1719 87.9896 61.8797 87.9023 61.8797 87.9023L42.6578 97.8752Z"
              fill="white"
            />
            <path
              d="M78.9786 87.9102C78.9786 87.9102 77.2613 94.4315 80.7396 97.8736C84.2179 101.316 91.3389 100.876 95.2047 96.3749C91.5052 89.6263 78.9786 87.9102 78.9786 87.9102Z"
              fill="#738DE0"
            />
            <path
              d="M38.301 106.5C38.301 106.5 44.8749 108.873 46.614 115.207C42.7006 121.396 36.6484 119.506 36.6484 119.506"
              fill="#738DE0"
            />
            <path
              d="M86.8359 100.148C88.8565 100.148 90.3239 100.827 91.2036 102.124C91.8732 103.111 93.8425 105.507 96.0425 104.187C98.9219 102.469 94.4951 96.4901 94.4951 96.4901L88.9862 96.0664L86.8359 100.148Z"
              fill="#738DE0"
            />
            <path
              d="M42.1016 99.3689C42.1016 99.3689 45.3454 106.855 53.487 106.849C60.6195 106.849 65.1276 98.674 62.9607 93.0117C55.9638 95.8601 42.1016 99.3689 42.1016 99.3689Z"
              fill="#F0F4FF"
            />
            <path
              d="M40.4056 77.104C40.4056 77.104 38.423 72.4821 39.6692 70.5481C40.9155 68.6142 44.9471 75.441 44.9471 75.441L40.4056 77.104Z"
              fill="#FEC272"
            />
            <path
              d="M61.8206 77.104C61.8206 77.104 63.799 72.4821 62.5531 70.5481C61.3072 68.6142 57.2734 75.441 57.2734 75.441L61.8206 77.104Z"
              fill="#FEC272"
            />
            <path
              d="M64.4424 88.7984C63.8107 96.1577 61.3439 102.496 49.5154 101.478C37.6869 100.461 36.1359 93.7783 36.7676 86.4196C37.3992 79.061 44.1077 73.629 51.7498 74.284C59.3919 74.9389 65.074 81.4391 64.4424 88.7984Z"
              fill="white"
            />
            <path
              d="M48.2879 74.4023C48.2879 74.4023 32.8517 75.7387 31.1637 81.1126C30.0552 84.6521 35.7679 84.4629 39.4562 80.7022C40.2016 79.2608 48.2879 74.4023 48.2879 74.4023Z"
              fill="white"
            />
            <path
              d="M52.8125 74.4023C52.8125 74.4023 68.2469 75.7387 69.9339 81.1126C71.0445 84.6521 65.3315 84.4629 61.6414 80.7022C60.897 79.2608 52.8125 74.4023 52.8125 74.4023Z"
              fill="#738DE0"
            />
            <path
              d="M41.6362 90.2002C42.652 90.2002 43.4755 89.3763 43.4755 88.3599C43.4755 87.3435 42.652 86.5195 41.6362 86.5195C40.6204 86.5195 39.7969 87.3435 39.7969 88.3599C39.7969 89.3763 40.6204 90.2002 41.6362 90.2002Z"
              fill="#738DE0"
            />
            <path
              d="M59.4096 91.7276C60.4254 91.7276 61.2489 90.9036 61.2489 89.8872C61.2489 88.8708 60.4254 88.0469 59.4096 88.0469C58.3938 88.0469 57.5703 88.8708 57.5703 89.8872C57.5703 90.9036 58.3938 91.7276 59.4096 91.7276Z"
              fill="#738DE0"
            />
            <path
              d="M56.6582 94.0694C56.454 96.452 53.8572 98.172 49.8864 97.8322C45.9156 97.4924 43.7806 95.3612 43.9907 92.9793C44.2008 90.5974 46.5779 89.8085 50.5469 90.1501C54.5159 90.4917 56.8645 91.6871 56.6582 94.0694Z"
              fill="#FF97C9"
            />
            <g style={{ mixBlendMode: "multiply" }}>
              <path
                d="M48.0611 92.5429C48.1033 92.0512 47.7213 91.6169 47.2078 91.5727C46.6942 91.5286 46.2437 91.8915 46.2015 92.3832C46.1592 92.8748 46.5413 93.3092 47.0548 93.3533C47.5683 93.3974 48.0188 93.0346 48.0611 92.5429Z"
                fill="#FF97C9"
              />
            </g>
            <g style={{ mixBlendMode: "multiply" }}>
              <path
                d="M54.4126 93.0898C54.4549 92.5981 54.0728 92.1637 53.5593 92.1196C53.0458 92.0755 52.5953 92.4383 52.553 92.93C52.5108 93.4217 52.8928 93.8561 53.4064 93.9002C53.9199 93.9443 54.3704 93.5815 54.4126 93.0898Z"
                fill="#FF97C9"
              />
            </g>
            <path
              d="M51.9936 74.3086C51.9936 74.3086 49.3196 79.2244 55.3825 82.8516C59.9182 85.5625 63.8448 83.6439 63.8448 83.6439C63.8448 83.6439 62.1752 75.095 51.9936 74.3086Z"
              fill="#738DE0"
            />
            <path
              d="M78.9125 101.586C80.7862 101.776 81.8777 105.633 80.2805 106.849C78.6833 108.065 76.0119 105.978 75.8478 104.218C75.6836 102.458 76.5347 101.335 78.9125 101.586Z"
              fill="#738DE0"
            />
            <path
              d="M58.4467 104.121C59.103 102.149 63.5987 102.556 65.6171 104.384C67.6355 106.213 68.647 110.024 67.6376 110.742C66.6282 111.459 64.542 109.161 62.6766 108.491C60.8112 107.821 57.8091 106.03 58.4467 104.121Z"
              fill="#738DE0"
            />
            <g style={{ mixBlendMode: "soft-light", opacity: "0.42" }}>
              <path
                d="M77.8963 11.2506C78.4987 8.86564 74.5569 5.81333 69.092 4.43303C63.6272 3.05272 58.7087 3.86713 58.1063 6.25204C57.5039 8.63696 61.4458 11.6893 66.9106 13.0696C72.3755 14.4499 77.294 13.6355 77.8963 11.2506Z"
                fill="white"
              />
            </g>
          </svg>
        </div>
        <p className="text-[#414651] text-sm font-semibold">
          Không có thông báo nào!
        </p>
        <Button variant="primary" onClick={() => router.back()} className="">
          Trở về
        </Button>
      </div>
    </div>
  );
};

export default NotificationsPage;
