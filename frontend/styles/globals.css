@tailwind base;
@tailwind components;
@tailwind utilities;
/* @import "tailwind.config.css"; */

.width100 {
    width: 100% !important;
}
.width75 {
    width: 75% !important;
}
.width50 {
    width: 50% !important;
}
.width25 {
    width: 25% !important;
}
.border-gray{
    border: 1px solid #80808040;
}
.color-fa-play{
    color: #ffffffc4
}
.text-utility-blue-600 {
    color: #1570EF;
}
.text-tertiary-fg{
    color: #535862;
}
.text-primary-900{
    color: #181D27;
}
.text-quaternary-500{
    color: #717680;
}
.text-tertiary-600{
    color: #535862;
}
.border-secondary{
    color: #E9EAEB;
}
.text-utility-blue-50 {
    color: #EFF8FF;
}
.text-utility-brand-200 {
    color: #B5F2D7;
}
.text-utility-brand-700 {
    color: #198C43;
}
.button-secondary-border {
    color: #D5D7DA
}
.border-primary {
    color: #D5D7DA
}
/*.border-utility-brand-200{*/
/*    color: #B5F2D7*/
/*}*/

.shadow-skeu-var {
    box-shadow:
            inset 0px 0px 0px 1px rgba(10, 13, 18, 0.18),
            inset 0px -2px 0px 0px rgba(10, 13, 18, 0.05),
            0px 1px 2px 0px rgba(10, 13, 18, 0.05)
}
.shadow-xs {
    box-shadow: 0px 1px 2px 0px rgba(10, 13, 18, 0.05)
}