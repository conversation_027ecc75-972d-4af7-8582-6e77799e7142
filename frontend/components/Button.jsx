"use client";

import React from "react";

const Button = ({
  variant = "primary",
  children,
  className = "",
  icon,
  iconPosition = "left", // 'left', 'right', or 'center'
  ...props
}) => {
  const baseStyles =
    "px-3 py-2 border rounded-lg font-semibold flex justify-center items-center gap-[6px]";

  const variants = {
    primary:
      "bg-primary-default text-primary-default border-primary-default hover:bg-primary-hover hover:text-primary-hover hover:border-primary-hover focus:bg-primary-focused focus:text-primary-focused focus:border-primary-focused active:bg-primary-pressed active:text-primary-pressed disabled:bg-primary-disabled disabled:text-primary-disabled disabled:border-primary-disabled",

    secondaryGray:
      "bg-secondary-gray-default text-secondary-gray-default border-secondary-gray-default hover:bg-secondary-gray-hover hover:text-secondary-gray-hover hover:border-secondary-gray-hover focus:bg-secondary-gray-focused focus:text-secondary-gray-focused focus:border-secondary-gray-focused active:bg-secondary-gray-pressed active:text-secondary-gray-pressed active:border-secondary-gray-pressed disabled:bg-secondary-gray-disabled disabled:text-secondary-gray-disabled disabled:border-secondary-gray-disabled",

    secondaryColor:
      "bg-secondary-color-default text-secondary-color-default border-secondary-color-default hover:bg-secondary-color-hover focus:bg-secondary-color-focused active:bg-secondary-color-pressed disabled:bg-secondary-color-disabled",
  };

  const variantClasses = variants[variant] || "";

  return (
    <button
      className={`${baseStyles} ${variantClasses} ${className}`}
      {...props}
    >
      {iconPosition === "left" && icon && <span key="left-icon">{icon}</span>}
      {iconPosition === "center" && !children && icon && (
        <span key="center-icon">{icon}</span>
      )}
      {children && (
        <>
          {iconPosition === "center" && icon && (
            <span key="center-icon-before" className="mr-1">
              {icon}
            </span>
          )}
          <span key="children">{children}</span>
          {iconPosition === "center" && icon && (
            <span key="center-icon-after" className="ml-1">
              {icon}
            </span>
          )}
        </>
      )}
      {iconPosition === "right" && icon && <span key="right-icon">{icon}</span>}
    </button>
  );
};

export default Button;
