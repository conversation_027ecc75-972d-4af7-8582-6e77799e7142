"use client";

import React from "react";

/**
 * Component hiển thị trạng thái kích ho<PERSON>
 * @param {Object} props
 * @param {boolean} props.isActivated - Trạng thái kích ho<PERSON>t (true: đ<PERSON> kích ho<PERSON>, false: ch<PERSON><PERSON> k<PERSON><PERSON> ho<PERSON>)
 * @returns {JSX.Element}
 */
const StatusActivationBadge = ({ isActivated }) => {
  if (isActivated) {
    return (
      <div className="inline-flex items-center gap-1 pr-[6px] pl-[4px] py-[2px] rounded-full border border-[#17B26A] bg-[#ABEFC6]">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="12"
          height="12"
          viewBox="0 0 12 12"
          fill="none"
        >
          <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M11 6C11 8.7615 8.7615 11 6 11C3.2385 11 1 8.7615 1 6C1 3.2385 3.2385 1 6 1C8.7615 1 11 3.2385 11 6ZM8.407 4.7905C8.44515 4.73705 8.4724 4.67661 8.48719 4.61263C8.50198 4.54866 8.50403 4.48239 8.49321 4.41762C8.48239 4.35285 8.45892 4.29085 8.42414 4.23515C8.38936 4.17945 8.34395 4.13115 8.2905 4.093C8.23705 4.05485 8.17661 4.0276 8.11263 4.01281C8.04866 3.99802 7.98239 3.99597 7.91762 4.00679C7.85285 4.01761 7.79085 4.04108 7.73515 4.07586C7.67945 4.11064 7.63115 4.15605 7.593 4.2095L5.436 7.2295L4.3535 6.1465C4.2592 6.05542 4.1329 6.00502 4.0018 6.00616C3.8707 6.0073 3.74529 6.05989 3.65259 6.15259C3.55989 6.24529 3.5073 6.3707 3.50616 6.5018C3.50502 6.6329 3.55542 6.7592 3.6465 6.8535L5.1465 8.3535C5.19785 8.40477 5.25974 8.44425 5.32788 8.4692C5.39602 8.49414 5.46877 8.50396 5.54109 8.49796C5.6134 8.49197 5.68354 8.47031 5.74665 8.43448C5.80975 8.39866 5.86429 8.34952 5.9065 8.2905L8.407 4.7905Z"
            fill="#17B26A"
          />
        </svg>
        <span className="text-xs leading-[18px] text-[#17B26A] font-medium">
          Đã kích hoạt
        </span>
      </div>
    );
  }

  return (
    <div className="inline-flex items-center gap-1 pr-[6px] pl-[4px] py-[2px] rounded-full border border-[#F79009] bg-[#FFFAEB]">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="12"
        height="12"
        viewBox="0 0 12 12"
        fill="none"
      >
        <path
          d="M8.5 1.66995C9.25413 2.10537 9.88147 2.73021 10.3199 3.4826C10.7583 4.23498 10.9927 5.08883 10.9997 5.95962C11.0067 6.8304 10.7862 7.68792 10.36 8.44729C9.93378 9.20667 9.31662 9.84155 8.56962 10.2891C7.82262 10.7367 6.97168 10.9814 6.10105 10.999C5.23042 11.0166 4.37027 10.8065 3.60578 10.3896C2.84128 9.97261 2.19894 9.36321 1.74233 8.6217C1.28573 7.8802 1.0307 7.0323 1.0025 6.16195L1 5.99995L1.0025 5.83795C1.0305 4.97444 1.28177 4.13293 1.73182 3.39544C2.18187 2.65796 2.81533 2.04967 3.57045 1.62989C4.32557 1.2101 5.17657 0.993145 6.0405 1.00017C6.90443 1.00719 7.7518 1.23795 8.5 1.66995ZM6 2.99995C5.87753 2.99996 5.75933 3.04492 5.66781 3.1263C5.5763 3.20768 5.51783 3.31982 5.5035 3.44145L5.5 3.49995V5.99995L5.5045 6.06545C5.5159 6.15219 5.54986 6.23443 5.603 6.30395L5.6465 6.35395L7.1465 7.85395L7.1935 7.89495C7.28119 7.96298 7.38902 7.9999 7.5 7.9999C7.61098 7.9999 7.71881 7.96298 7.8065 7.89495L7.8535 7.85345L7.895 7.80645C7.96303 7.71876 7.99996 7.61093 7.99996 7.49995C7.99996 7.38896 7.96303 7.28113 7.895 7.19345L7.8535 7.14645L6.5 5.79245V3.49995L6.4965 3.44145C6.48217 3.31982 6.4237 3.20768 6.33219 3.1263C6.24067 3.04492 6.12247 2.99996 6 2.99995Z"
          fill="#F79009"
        />
      </svg>
      <span className="text-xs leading-[18px] text-[#B54708] font-medium">
        Chưa kích hoạt
      </span>
    </div>
  );
};

export default StatusActivationBadge;
