"use client";

import React, {useEffect, useRef, useState} from "react";
import LineDivider from "@/components/icons/LineDivider";
import {useScreenSize} from "@/hooks/useScreenSize";
import clsx from "clsx";

const ViewFullImageModal = ({isOpen, onClose, imagePath}) => {
    if (!isOpen) return;
    const screenSize = useScreenSize();
    useEffect(() => {
        if (!isOpen) return;
        const handleKeyDown = (e) => {
            if (e.key === "Escape") onClose();
        };
        window.addEventListener("keydown", handleKeyDown);
        return () => window.removeEventListener("keydown", handleKeyDown);
    }, [isOpen, onClose]);

    return (
        <div className="fixed inset-0  flex items-center justify-center bg-[#000000] bg-opacity-60 px-4"
             style={{zIndex: 100}}
             tabIndex={-1}
             aria-modal="true"
             role="dialog">
            <div className="relative w-[994px] aspect-[994.00/269.67]">
                <button className={clsx(
                    "absolute text-md leading-md font-normal text-[#D5D7DA] underline underline-offset-auto",
                    screenSize?.bw375640 ? "bottom-[-33px] right-1/2 left-1/2" : "top-[-33px] right-0"
                )} onClick={onClose}>
                    Đóng
                </button>
                <img src={imagePath} className="object-cover no-repeat" />
            </div>
        </div>
    // <div className="bg-[#FFFFFF] shadow-xl relative overflow-y-auto flex flex-col gap-y-[13px]">
    //     <div className="relative w-[994px] aspect-[994.00/269.67]">
    //         <button className="absolute top-[-20px] right-0 text-md leading-md font-normal text-brand-600 z-[110]" onClick={onClose}>
    //             Đóng
    //         </button>
    //         <img src={imagePath} className="object-cover no-repeat" />
    //     </div>
    // </div>

);
};

export default ViewFullImageModal;
