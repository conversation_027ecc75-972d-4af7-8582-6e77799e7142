"use client";

import React from "react";

/**
 * Component hiển thị trạng thái dựa vào ng<PERSON> tháng
 * @param {Object} props
 * @param {string} props.status - Trạng thái trực tiếp (ongoing, upcoming, expired) - c<PERSON> thể bỏ qua nếu có startDate và endDate
 * @param {string|Date} props.startDate - <PERSON><PERSON><PERSON> bắt đ<PERSON> (định dạng ISO string hoặc Date object)
 * @param {string|Date} props.endDate - <PERSON><PERSON><PERSON> kế<PERSON> thú<PERSON> (định dạng ISO string hoặc Date object)
 * @returns {JSX.Element}
 */
const StatusBadge = ({ status, startDate, endDate }) => {
  const getStatusFromDates = () => {
    // Nếu có status trực tiếp, ưu tiên sử dụng
    if (status) return status;

    // Nếu không có ngày, trả về ongoing
    if (!startDate || !endDate) return "ongoing";

    const now = new Date();
    const start = new Date(startDate);
    const end = new Date(endDate);

    if (now < start) return "upcoming";
    if (now > end) return "expired";
    return "ongoing";
  };

  const getStatusStyles = () => {
    const currentStatus = getStatusFromDates();

    switch (currentStatus) {
      case "ongoing":
        return {
          dotColor: "#17B26A",
          text: "Đang diễn ra",
        };
      case "upcoming":
        return {
          dotColor: "#2E90FA",
          text: "Sắp diễn ra",
        };
      case "expired":
        return {
          dotColor: "#717680",
          text: "Hết hiệu lực",
        };
      default:
        return {
          dotColor: "#17B26A",
          text: "Đang diễn ra",
        };
    }
  };

  const styles = getStatusStyles();

  return (
    <span
      className={`inline-flex items-center gap-1 px-[6px] py-[2px] rounded-[6px] border border-[#D5D7DA] text-[#414651] text-xs font-medium bg-[#FFFFFF]`}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="8"
        height="8"
        viewBox="0 0 8 8"
        fill="none"
      >
        <circle cx="4" cy="4" r="3" fill={styles.dotColor} />
      </svg>
      {styles.text}
    </span>
  );
};

export default StatusBadge;
