import React, { useEffect } from "react";

const ICONS = {
  success: (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="25"
      viewBox="0 0 24 25"
      fill="none"
    >
      <path
        d="M7.5 12.5L10.5 15.5L16.5 9.5M22 12.5C22 18.0228 17.5228 22.5 12 22.5C6.47715 22.5 2 18.0228 2 12.5C2 6.97715 6.47715 2.5 12 2.5C17.5228 2.5 22 6.97715 22 12.5Z"
        stroke="#079455"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  ),
  error: (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M18 6L6 18M6 6L18 18"
        stroke="#D92D20"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  ),
  warning: (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M12 9V11M12 15H12.01M5.07 19H18.93C19.9175 19 20.4113 19 20.7355 18.7478C21.0598 18.4957 21.1675 18.0399 21.3828 17.1284L22.7182 11.1284C22.9067 10.3266 23.001 9.92573 22.8674 9.59485C22.7337 9.26398 22.4033 9.07116 21.7424 8.68551L13.4942 4.23336C12.8829 3.87798 12.5772 3.70029 12.2474 3.66522C11.9176 3.63015 11.5829 3.74164 10.9135 3.96461L2.52725 6.74564C1.84424 6.97315 1.50274 7.0869 1.29636 7.37359C1.08997 7.66028 1.07126 8.05656 1.03384 8.84911L0.623239 15.1491C0.579829 16.0814 0.558124 16.5476 0.731155 16.9148C0.904187 17.282 1.27499 17.4994 2.0166 17.9343L3.11284 18.5229C3.77836 18.8888 4.11112 19.0717 4.48256 19.0955C4.854 19.1193 5.21724 18.9827 5.94371 18.7095L5.07 19Z"
        stroke="#F79009"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  ),
  info: (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M12 16V12M12 8H12.01M22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12Z"
        stroke="#3538CD"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  ),
};

const COLORS = {
  success: {
    bg: "bg-[#299D55]",
    bgOpacity: "bg-opacity-10",
    text: "text-[#181D27]",
  },
  error: {
    bg: "bg-[#D92D20]",
    bgOpacity: "bg-opacity-10",
    text: "text-[#D92D20]",
  },
  warning: {
    bg: "bg-[#F79009]",
    bgOpacity: "bg-opacity-10",
    text: "text-[#F79009]",
  },
  info: {
    bg: "bg-[#3538CD]",
    bgOpacity: "bg-opacity-10",
    text: "text-[#3538CD]",
  },
};

const Notification = ({
  title,
  message,
  type = "success",
  duration,
  onClose,
  onAction,
  actionText,
}) => {
  useEffect(() => {
    if (duration) {
      const timer = setTimeout(() => {
        onClose();
      }, duration);
      return () => clearTimeout(timer);
    }
  }, [duration, onClose]);

  const colors = COLORS[type];

  return (
    <div className="fixed inset-0 flex items-center justify-center"
      style={{zIndex: 999}}
    >
      <div
        className="fixed inset-0 bg-[#181D27] bg-opacity-50 backdrop-blur-[8px]"
        onClick={onClose}
      ></div>
      <div className="bg-[#FFFFFF] rounded-lg p-6 relative z-10 max-w-[400px] w-full mx-4">
        <div className="flex items-center justify-center mb-4">
          <div
            className={`w-12 h-12 rounded-full ${colors.bg} ${colors.bgOpacity} flex items-center justify-center`}
          >
            {ICONS[type]}
          </div>
        </div>
        <h3
          className={`text-center text-lg font-semibold mb-1 ${colors.text} `}
        >
          {title}
        </h3>
        <p className="text-center text-[#535862] text-sm font-normal mb-8">
          {message}
        </p>
        {actionText && onAction && (
          <div className="flex justify-center mt-4">
            <button
              onClick={onAction}
              className={`w-full px-4 py-2 rounded-lg ${colors.bg} text-[#FFFFFF] text-base font-semibold `}
            >
              {actionText}
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default Notification;
