"use client";

import React, { useEffect, useState } from "react";
import Link from "next/link";
import Image from "next/image";
import Button from "../components/Button";
import { useRouter } from "next/navigation";
import ProfileModal from "../components/dashboard/ProfileModal";
import PasswordChangeModal from "../components/dashboard/PasswordChangeModal";
import strapi from "../app/api/strapi";

const MobileMenu = ({
  isOpen,
  onClose,
  user,
  isAuthenticated,
  handleLogout,
}) => {
  const [isProfileModalOpen, setIsProfileModalOpen] = useState(false);
  const [isPasswordChangeModalOpen, setIsPasswordChangeModalOpen] =
    useState(false);
  const [liveStreamCourses, setLiveStreamCourses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // M<PERSON>ng các màu cố định cho background item
  const backgroundColors = ["#3CCB7F", "#528BFF", "#FD6F8E"];

  const handleOpenProfile = () => {
    setIsProfileModalOpen(true);
  };

  const handleCloseProfile = () => {
    setIsProfileModalOpen(false);
  };

  const handleOpenPasswordChange = () => {
    setIsPasswordChangeModalOpen(true);
  };

  const handleClosePasswordChange = () => {
    setIsPasswordChangeModalOpen(false);
  };

  const router = useRouter();

  // Lấy dữ liệu khóa học Livestream từ API
  useEffect(() => {
    const fetchLiveStreamCourses = async () => {
      if (!isOpen) return; // Chỉ fetch khi menu mở

      try {
        setLoading(true);
        // Giả định rằng API strapi có endpoint để lấy các khóa học Livestream
        const response = await strapi.grade.getGrade();
        setLiveStreamCourses(response.data || []);
        setError(null);
      } catch (err) {
        console.error("Lỗi khi lấy dữ liệu khóa học Livestream:", err);
        setError(err);
      } finally {
        setLoading(false);
      }
    };

    fetchLiveStreamCourses();
  }, [isOpen]);

  const getUserData = () => {
    if (!user) {
      return {
        name: "User",
        fullname: "Người dùng",
        email: "<EMAIL>",
        imageUrl: null,
      };
    }

    const getLastName = (fullname) => {
      const nameParts = fullname?.trim().split(" ") || ["User"];
      return nameParts[nameParts.length - 1];
    };

    return {
      ...user,
      name: user.fullname ? getLastName(user.fullname) : "User",
      email: user.email || "<EMAIL>",
      fullname: user.fullname || "Người dùng",
      imageUrl: user.image?.[0]?.url
        ? process.env.NEXT_PUBLIC_STRAPI_URL + user.image[0].url
        : null,
    };
  };

  useEffect(() => {
    // Vô hiệu hóa cuộn khi menu mở
    if (isOpen) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "auto";
    }

    return () => {
      document.body.style.overflow = "auto";
    };
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <div
      className="fixed top-0 left-0 w-full h-screen z-40 md:flex md:justify-end"
      onClick={onClose}
    >
      {/* Nền mờ khi mở menu */}
      <div className="absolute inset-0 bg-[#000000] bg-opacity-50 transition-opacity duration-300"></div>

      {/* Menu chính - Thay đổi width theo thiết bị */}
      <div
        className="fixed top-0 right-0 h-full bg-[#FFFFFF] shadow-xl z-50 transform transition-transform duration-300 ease-in-out translate-x-0 sm:w-[321px] w-full flex flex-col"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header với logo và nút đóng */}
        <div className="flex justify-between items-center px-4 py-3 bg-[#FFFFFF] border-b border-[#E9EAEB]">
          <div className="flex-1 flex items-center relative">
            <Image
              src="/images/homepage/logo.png"
              alt="Logo"
              className="cursor-pointer object-cover"
              width={87}
              height={32}
            />
            <button
              onClick={onClose}
              className="absolute right-0 focus:outline-none"
              aria-label="Đóng menu"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
              >
                <path
                  d="M18 6L6 18M6 6L18 18"
                  stroke="#414651"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </button>
          </div>
        </div>

        {/* Thông tin người dùng */}
        {isAuthenticated && user && (
          <div className="px-6 py-4 flex items-center space-x-3">
            {getUserData().imageUrl ? (
              <Image
                src={getUserData().imageUrl}
                alt="Profile"
                className="w-10 h-10 rounded-full object-cover"
                width={40}
                height={40}
              />
            ) : (
              <div className="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center">
                <span className="text-gray-500 text-lg font-medium">
                  {getUserData().name?.charAt(0)?.toUpperCase() || "U"}
                </span>
              </div>
            )}
            <div>
              <div className="font-medium text-gray-900">
                {getUserData().fullname || "Olivia Rhye"}
              </div>
              <div className="text-sm text-gray-500">
                {getUserData().email || "<EMAIL>"}
              </div>
            </div>
          </div>
        )}

        {/* Nội dung menu có thể cuộn - chiếm phần lớn không gian */}
        <div className="flex-1 overflow-y-auto">
          {/* Tiêu đề và danh sách khóa học */}
          <div className="px-4 py-5 border-b border-[#E9EAEB]">
            <div className="p-4 flex flex-col gap-4">
              <h2 className="text-lg font-medium text-center ">
                Tìm hiểu ngay?
              </h2>

              <div className="space-y-3">
                {loading ? (
                  <div className="flex justify-center py-4">
                    <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-[#45BF76]"></div>
                  </div>
                ) : error ? (
                  <div className="text-center text-red-500 py-3">
                    Không thể tải dữ liệu. Vui lòng thử lại.
                  </div>
                ) : (
                  liveStreamCourses.map((course, index) => (
                    <Link
                      key={course.id}
                      href={`/khoa-hoc/${course.course.slug}`}
                      className="flex items-center justify-between text-[#FFFFFF] text-base font-semibold py-3 px-4 rounded-lg"
                      onClick={onClose}
                      style={{
                        backgroundColor:
                          backgroundColors[index % backgroundColors.length],
                      }}
                    >
                      <div className="flex items-center gap-1.5">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="21"
                          height="20"
                          viewBox="0 0 21 20"
                          fill="none"
                        >
                          <path
                            d="M18.8327 7.44283C18.8327 6.93798 18.8327 6.68556 18.7329 6.56867C18.6462 6.46725 18.5163 6.41343 18.3833 6.42389C18.2301 6.43595 18.0516 6.61444 17.6946 6.97142L14.666 10L17.6946 13.0286C18.0516 13.3856 18.2301 13.5641 18.3833 13.5761C18.5163 13.5866 18.6462 13.5328 18.7329 13.4314C18.8327 13.3145 18.8327 13.0621 18.8327 12.5572V7.44283Z"
                            fill="white"
                          />
                          <path
                            d="M2.16602 8.16669C2.16602 6.76656 2.16602 6.06649 2.4385 5.53171C2.67818 5.06131 3.06063 4.67885 3.53104 4.43917C4.06582 4.16669 4.76588 4.16669 6.16602 4.16669H10.666C12.0661 4.16669 12.7662 4.16669 13.301 4.43917C13.7714 4.67885 14.1538 5.06131 14.3935 5.53171C14.666 6.06649 14.666 6.76656 14.666 8.16669V11.8334C14.666 13.2335 14.666 13.9336 14.3935 14.4683C14.1538 14.9387 13.7714 15.3212 13.301 15.5609C12.7662 15.8334 12.0661 15.8334 10.666 15.8334H6.16602C4.76588 15.8334 4.06582 15.8334 3.53104 15.5609C3.06063 15.3212 2.67818 14.9387 2.4385 14.4683C2.16602 13.9336 2.16602 13.2335 2.16602 11.8334V8.16669Z"
                            fill="white"
                          />
                          <path
                            d="M18.8327 7.44283C18.8327 6.93798 18.8327 6.68556 18.7329 6.56867C18.6462 6.46725 18.5163 6.41343 18.3833 6.42389C18.2301 6.43595 18.0516 6.61444 17.6946 6.97142L14.666 10L17.6946 13.0286C18.0516 13.3856 18.2301 13.5641 18.3833 13.5761C18.5163 13.5866 18.6462 13.5328 18.7329 13.4314C18.8327 13.3145 18.8327 13.0621 18.8327 12.5572V7.44283Z"
                            stroke="white"
                            strokeWidth="1.75"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                          <path
                            d="M2.16602 8.16669C2.16602 6.76656 2.16602 6.06649 2.4385 5.53171C2.67818 5.06131 3.06063 4.67885 3.53104 4.43917C4.06582 4.16669 4.76588 4.16669 6.16602 4.16669H10.666C12.0661 4.16669 12.7662 4.16669 13.301 4.43917C13.7714 4.67885 14.1538 5.06131 14.3935 5.53171C14.666 6.06649 14.666 6.76656 14.666 8.16669V11.8334C14.666 13.2335 14.666 13.9336 14.3935 14.4683C14.1538 14.9387 13.7714 15.3212 13.301 15.5609C12.7662 15.8334 12.0661 15.8334 10.666 15.8334H6.16602C4.76588 15.8334 4.06582 15.8334 3.53104 15.5609C3.06063 15.3212 2.67818 14.9387 2.4385 14.4683C2.16602 13.9336 2.16602 13.2335 2.16602 11.8334V8.16669Z"
                            stroke="white"
                            strokeWidth="1.75"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </svg>
                        {course.title}
                      </div>
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="20"
                        height="20"
                        viewBox="0 0 20 20"
                        fill="none"
                      >
                        <path
                          d="M4.16602 10H15.8327M15.8327 10L9.99935 4.16669M15.8327 10L9.99935 15.8334"
                          stroke="white"
                          strokeWidth="1.75"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </svg>
                    </Link>
                  ))
                )}
              </div>
            </div>
          </div>

          {/* Danh sách các liên kết */}
          <div className="py-5 ">
            <div className="space-y-3">
              <Link
                href="/hoc-tai-trung-tam"
                className="flex items-center gap-3 py-3 px-4"
                onClick={onClose}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="25"
                  height="24"
                  viewBox="0 0 25 24"
                  fill="none"
                >
                  <path
                    d="M2.5 9.45452L5.35714 7.76906M5.35714 7.76906V16.9545C5.35714 18.3182 6.07143 19 8 19H9.64286M5.35714 7.76906L11.3029 4.26043C12.0595 3.91319 12.9405 3.91319 13.6971 4.26043L19.6429 7.76906M9.64286 19V17.6364C9.64286 16.1301 10.922 14.9091 12.5 14.9091C14.078 14.9091 15.3571 16.1301 15.3571 17.6364V19M9.64286 19H15.3571M19.6429 7.76906V16.9545C19.6429 18.3182 18.9286 19 17 19H15.3571M19.6429 7.76906L22.5 9.45452"
                    stroke="#717680"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
                <span className="font-semibold text-[#414651] text-base">
                  Học tại Trung tâm
                </span>
              </Link>

              <Link
                href="/bai-viet"
                className="flex items-center gap-3 py-3 px-4"
                onClick={onClose}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="25"
                  height="24"
                  viewBox="0 0 25 24"
                  fill="none"
                >
                  <path
                    d="M18.8 7.52941V15.4706M7.1 8.41176H14.3M12.5 11.9412H14.3M12.5 14.5882H14.3M3.5 7C3.5 5.34315 4.84315 4 6.5 4L15.5857 4C17.2425 4 18.7219 5.20956 18.7219 6.86641C18.7219 6.98476 18.8182 7.0799 18.9363 7.08824C20.4931 7.19812 21.5 8.62949 21.5 10.2143V16C21.5 17.6569 20.1569 19 18.5 19H6.5C4.84315 19 3.5 17.6569 3.5 16V7ZM7.1 11.9412H9.8V14.5882H7.1V11.9412Z"
                    stroke="#717680"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
                <span className="font-semibold text-[#414651] text-base">
                  Bài viết
                </span>
              </Link>

              {isAuthenticated && (
                <>
                  <button
                    onClick={() => {
                      handleOpenProfile();
                    }}
                    className="flex items-center gap-3 py-3 px-4"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="25"
                      height="24"
                      viewBox="0 0 25 24"
                      fill="none"
                    >
                      <path
                        d="M12.4999 15C9.32977 15 6.51065 16.5306 4.71585 18.906C4.32956 19.4172 4.13641 19.6728 4.14273 20.0183C4.14761 20.2852 4.31521 20.6219 4.52522 20.7867C4.79704 21 5.17372 21 5.92708 21H19.0726C19.826 21 20.2027 21 20.4745 20.7867C20.6845 20.6219 20.8521 20.2852 20.857 20.0183C20.8633 19.6728 20.6701 19.4172 20.2839 18.906C18.4891 16.5306 15.6699 15 12.4999 15Z"
                        stroke="#717680"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                      <path
                        d="M12.4999 12C14.9851 12 16.9999 9.98528 16.9999 7.5C16.9999 5.01472 14.9851 3 12.4999 3C10.0146 3 7.99985 5.01472 7.99985 7.5C7.99985 9.98528 10.0146 12 12.4999 12Z"
                        stroke="#717680"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                    <span className="font-semibold text-[#414651] text-base">
                      Thông tin cá nhân
                    </span>
                  </button>

                  <button
                    onClick={() => {
                      handleOpenPasswordChange();
                    }}
                    className="flex items-center gap-3 py-3 px-4"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="25"
                      height="24"
                      viewBox="0 0 25 24"
                      fill="none"
                    >
                      <path
                        d="M17.5 10V8C17.5 5.23858 15.2614 3 12.5 3C9.73858 3 7.5 5.23858 7.5 8V10M12.5 14.5V16.5M9.3 21H15.7C17.3802 21 18.2202 21 18.862 20.673C19.4265 20.3854 19.8854 19.9265 20.173 19.362C20.5 18.7202 20.5 17.8802 20.5 16.2V14.8C20.5 13.1198 20.5 12.2798 20.173 11.638C19.8854 11.0735 19.4265 10.6146 18.862 10.327C18.2202 10 17.3802 10 15.7 10H9.3C7.61984 10 6.77976 10 6.13803 10.327C5.57354 10.6146 5.1146 11.0735 4.82698 11.638C4.5 12.2798 4.5 13.1198 4.5 14.8V16.2C4.5 17.8802 4.5 18.7202 4.82698 19.362C5.1146 19.9265 5.57354 20.3854 6.13803 20.673C6.77976 21 7.61984 21 9.3 21Z"
                        stroke="#717680"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                    <span className="font-semibold text-[#414651] text-base">
                      Đổi mật khẩu
                    </span>
                  </button>
                </>
              )}
            </div>
          </div>
        </div>

        {/* Footer - Button */}
        <div className="mt-auto p-4 bg-[#FFFFFF]">
          {isAuthenticated ? (
            <Button
              onClick={() => {
                handleLogout();
                onClose();
              }}
              variant="secondaryGray"
              className="w-full"
            >
              Đăng xuất
            </Button>
          ) : (
            <Button
              onClick={() => {
                router.push("/dang-nhap");
                onClose();
              }}
              variant="secondaryColor"
              className="w-full"
            >
              Đăng nhập
            </Button>
          )}
        </div>
      </div>

      {isPasswordChangeModalOpen && (
        <div
          className="fixed inset-0 z-[60]"
          onClick={(e) => e.stopPropagation()}
        >
          <PasswordChangeModal
            isOpen={isPasswordChangeModalOpen}
            onClose={handleClosePasswordChange}
          />
        </div>
      )}

      {/* Đặt ProfileModal ở ngoài và ngăn sự kiện click để không bị đóng */}
      {isProfileModalOpen && (
        <div
          className="fixed inset-0 z-[60]"
          onClick={(e) => e.stopPropagation()}
        >
          <ProfileModal
            isOpen={isProfileModalOpen}
            onClose={handleCloseProfile}
          />
        </div>
      )}
    </div>
  );
};

export default MobileMenu;
