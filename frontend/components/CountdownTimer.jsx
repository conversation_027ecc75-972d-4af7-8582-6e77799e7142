"use client";

import { useState, useEffect } from "react";

export default function CountdownTimer({ targetDate }) {
  const [timeLeft, setTimeLeft] = useState({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0,
  });

  useEffect(() => {
    // Đả<PERSON> bảo targetDate là một đối tượng Date
    const targetTime = new Date(targetDate).getTime();

    const calculateTimeLeft = () => {
      const now = new Date().getTime();
      const difference = targetTime - now;

      if (difference > 0) {
        const days = Math.floor(difference / (1000 * 60 * 60 * 24));
        const hours = Math.floor(
          (difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)
        );
        const minutes = Math.floor(
          (difference % (1000 * 60 * 60)) / (1000 * 60)
        );
        const seconds = Math.floor((difference % (1000 * 60)) / 1000);

        setTimeLeft({ days, hours, minutes, seconds });
      } else {
        // Nếu đã đến hoặc qua ngày mục tiêu
        setTimeLeft({ days: 0, hours: 0, minutes: 0, seconds: 0 });
      }
    };

    // Tính toán lần đầu
    calculateTimeLeft();

    // Cập nhật mỗi giây
    const timer = setInterval(calculateTimeLeft, 1000);

    // Dọn dẹp interval khi component unmount
    return () => clearInterval(timer);
  }, [targetDate]);

  // Hàm để thêm số 0 phía trước nếu số < 10
  const padWithZero = (num) => {
    return num < 10 ? `0${num}` : num;
  };

  return (
    <div className="flex flex-wrap justify-center md:justify-start gap-2 md:gap-3">
      <div className="w-[65px] sm:w-[100px] bg-[#fff] md:w-[90px] h-[80px] md:h-[90px] bg-white/70 border-2 border-[#E9EAEB] rounded-2xl flex flex-col items-center justify-center">
        <span className="text-2xl font-semibold text-[#181D27]">
          {padWithZero(timeLeft.days)}
        </span>
        <span className="text-sm font-medium text-[#535862]">ngày</span>
      </div>
      <div className="w-[65px] sm:w-[100px] bg-[#fff] md:w-[90px] h-[80px] md:h-[90px] bg-white/70 border-2 border-[#E9EAEB] rounded-2xl flex flex-col items-center justify-center">
        <span className="text-2xl font-semibold text-[#181D27]">
          {padWithZero(timeLeft.hours)}
        </span>
        <span className="text-sm font-medium text-[#535862]">giờ</span>
      </div>
      <div className="w-[65px] sm:w-[100px] bg-[#fff] md:w-[90px] h-[80px] md:h-[90px] bg-white/70 border-2 border-[#E9EAEB] rounded-2xl flex flex-col items-center justify-center">
        <span className="text-2xl font-semibold text-[#181D27]">
          {padWithZero(timeLeft.minutes)}
        </span>
        <span className="text-sm font-medium text-[#535862]">phút</span>
      </div>
      <div className="w-[65px] sm:w-[100px] bg-[#fff] md:w-[90px] h-[80px] md:h-[90px] bg-white/70 border-2 border-[#E9EAEB] rounded-2xl flex flex-col items-center justify-center">
        <span className="text-2xl font-semibold text-[#181D27]">
          {padWithZero(timeLeft.seconds)}
        </span>
        <span className="text-sm font-medium text-[#535862]">giây</span>
      </div>
    </div>
  );
}
