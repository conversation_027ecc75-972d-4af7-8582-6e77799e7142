"use client";

import React, {useEffect, useRef, useState} from "react";
import clsx from "clsx";
import LineDivider from "@/components/icons/LineDivider";
import strapi from "@/app/api/strapi";
import Cookies from "universal-cookie";
import {CommonUtil} from "@/utils/CommonUtil";
import {useScreenSize} from "@/hooks/useScreenSize";

/**
 * Popup hóa đơn mua khóa học
 * @param isOpen
 * @param onClose
 * @constructor
 */
const OutStreakPopup = ({isOpen, onClose, onSendData}) => {
    if (!isOpen) return;
    const [isUse, setIsUse] = useState(false);
    useEffect(() => {
        const handleKeyDown = (e) => {
            if (e.key === "Escape") onClose();
        };
        window.addEventListener("keydown", handleKeyDown);
        return () => window.removeEventListener("keydown", handleKeyDown);
    }, [isOpen, onClose]);
    const handleSend = () => {
        onSendData?.(true);
        onClose?.();
    };
    return (
        <div className="fixed inset-0  flex items-center justify-center bg-[#000000] bg-opacity-60 px-4"
             style={{zIndex: 120}}
            tabIndex={-1}
            aria-modal="true"
            role="dialog">
            <div className="bg-[#FFFFFF] rounded-2xl  shadow-xl w-[400px] relative max-h-screen overflow-y-auto">
                <div className="px-3xl pt-3xl">
                    <div className="header_model mb-xl">
                        <div className="w-full h-full relative flex justify-center">
                            <div className="w-[48px] h-[48px] p-[12px] flex justify-center items-center rounded-full bg-[#FEE4E2]">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                    <path d="M11.9998 9.00023V13.0002M11.9998 17.0002H12.0098M10.6151 3.89195L2.39019 18.0986C1.93398 18.8866 1.70588 19.2806 1.73959 19.6039C1.769 19.886 1.91677 20.1423 2.14613 20.309C2.40908 20.5002 2.86435 20.5002 3.77487 20.5002H20.2246C21.1352 20.5002 21.5904 20.5002 21.8534 20.309C22.0827 20.1423 22.2305 19.886 22.2599 19.6039C22.2936 19.2806 22.0655 18.8866 21.6093 18.0986L13.3844 3.89195C12.9299 3.10679 12.7026 2.71421 12.4061 2.58235C12.1474 2.46734 11.8521 2.46734 11.5935 2.58235C11.2969 2.71421 11.0696 3.10679 10.6151 3.89195Z" stroke="#D92D20" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                </svg>
                            </div>
                            <div className="button_close absolute right-0 top-0 cursor-pointer" onClick={onClose}>
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                    <path d="M18 6L6 18M6 6L18 18" stroke="#A4A7AE" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                </svg>
                            </div>
                        </div>
                    </div>
                    <div className="content_ flex flex-col text-center w-full">
                        <p className=" text-primary-900 text-lg leading-lg font-semibold w-full">
                            Á à, định thoát à?
                        </p>
                        <p className="text-tertiary-600 text-sm leading-sm font-normal">
                            Hoàn thành đi rồi tha hồ lướt Tiktok!
                        </p>
                    </div>
                </div>

                <div className="footer_ p-3xl gap-lg flex justify-center">
                    <button onClick={handleSend}  className="px-xl py-[10px]  w-[170px] rounded-md border border-secondary">
                        <p className="text-md leading-md font-semibold text-[#414651]"> Xíu em quay lại</p>
                    </button>
                    <button onClick={onClose} className="px-xl py-[10px]  w-[170px]  bg-[#299D55] rounded-md">
                        <p className="text-md leading-md font-semibold text-white">  Tiếp tục</p>

                    </button>
                </div>
            </div>

        </div>
    );
};

export default OutStreakPopup;
