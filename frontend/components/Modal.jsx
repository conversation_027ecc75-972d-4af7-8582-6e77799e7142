import LineDivider from "@/components/icons/LineDivider";
import clsx from "clsx";

export default function Modal({ isOpen, onClose, headerTitle , width ,children }) {
    if (!isOpen) return null;
    return (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 ">
            <div className={clsx(
                    "bg-white w-full relative rounded-xl shadow-lg border border-secondary",
                    "max-w-[" + width + "px]"
                )}
            >
                <div className="header_model px-3xl pt-3xl mb-[20px]">
                    <div className="w-full h-full relative text-center">
                        <p className="header_title text-primary-900 text-xl leading-xl font-semibold">
                            {headerTitle}
                        </p>
                        <div className="button_close absolute right-0 top-1/2 -translate-y-1/2 cursor-pointer" onClick={onClose}>
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                <path d="M18 6L6 18M6 6L18 18" stroke="#A4A7AE" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                            </svg>
                        </div>
                    </div>
                </div>
                <LineDivider></LineDivider>
                <div className="content_model py-4xl px-3xl text-primary-900">
                    {children}
                </div>
            </div>
        </div>
    );
}
