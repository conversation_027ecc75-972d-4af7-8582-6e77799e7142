import React from "react";
import { FaFacebook, FaTiktok, FaPhone } from "react-icons/fa";
import Image from "next/image";
import Link from "next/link";
import whiteLogo from "../public/images/white-logo.png"; // <PERSON><PERSON><PERSON> bảo đường dẫn logo là ch<PERSON>h xác
import { usePathname } from "next/navigation";

const Footer = ({ hideOnMobile = false }) => {
  const pathname = usePathname();
  const isCourse = pathname.startsWith("/khoa-hoc/");

  return (
    <footer
      className={`${
        hideOnMobile && isCourse ? "hidden md:block" : ""
      } bg-[#299D55] py-12 px-6 text-[#FFF] max-md:pt=12 max-md: pb-6`}
    >
      <div className="mx-auto max-w-[1440px]">
        {/* Container */}
        <div className="mx-auto flex justify-between items-start max-md:flex-col max-md:items-center max-md:text-center">
          {/* Left Side */}
          <div className="md:space-y-4 max-md:justify-center max-md:space-y-6 ">
            <div className="max-md:justify-items-center">
              <Image
                src={whiteLogo}
                alt="Logo"
                width={250}
                height={92}
                className="max-md:h-[52px] max-md:w-[144px]"
                priority
              />
            </div>
            <div className="flex items-center space-x-2 ">
              <FaPhone />
              <a
                href="tel:0828949479"
                className="text-[#FFF] font-normal text-base"
              >
                Hỗ trợ: 0828 949 479
              </a>
            </div>
          </div>

          {/* Right Side */}
          <div className="space-y-4 text-right leading-6 max-md:mt-6">
            <div>
              <h4 className="font-bold text-[20px]">Liên hệ và theo dõi</h4>
            </div>
            <div className="flex items-center justify-between ">
              <a
                href="https://www.facebook.com/anh.ba.87"
                className="text-2xl"
                target="_blank"
                rel="noopener noreferrer" // Quan trọng cho bảo mật
              >
                <FaFacebook className="h-9 w-9" />
              </a>
              <a
                href="https://www.tiktok.com/@o.ba.day.hoa"
                className="text-2xl"
                target="_blank"
                rel="noopener noreferrer"
              >
                <FaTiktok className="h-9 w-9" />
              </a>
              <a
                href="https://zalo.me/0828949479"
                className="text-2xl"
                target="_blank"
                rel="noopener noreferrer"
              >
                Zalo
              </a>
            </div>
            <div className="w-full">
              <button className="bg-[#FFFFFF] text-[#299D55] font-normal text-base py-[6px] px-3 rounded w-full">
                Liên hệ hợp tác
              </button>
            </div>
          </div>
        </div>

        {/* Divider */}
        <div className="mt-6 border-t border-white/50 mx-auto"></div>

        {/* Bottom Text */}
        <div className="mx-auto mt-6 flex justify-between text-base text-[#8EE5BA] font-normal max-md:flex-col max-md:items-center max-md:text-center">
          <div className="space-x-2 max-md:order-2 max-md:mt-6">
            <Link href="#" className="block sm:inline">
              Xây dựng bởi Team Thầy Ba
            </Link>
            <span className="hidden sm:inline">•</span>
            <Link href="#" className="block sm:inline">
              Copyright © 2025. All rights reserved.
            </Link>
          </div>
          <div className="space-x-2 text-base font-normal max-md:order-1">
            <Link href="/chinh-sach-bao-mat" className="block sm:inline">
              Bảo mật thông tin
            </Link>
            <span className="hidden sm:inline">•</span>
            <Link href="/dieu-khoan" className="block sm:inline">
              Điều khoản
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
