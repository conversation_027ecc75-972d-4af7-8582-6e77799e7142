import React from "react";

const RadioGroup = ({
  label,
  required = false,
  options,
  value,
  onChange,
  error,
  helperText,
  className = "",
}) => {
  return (
    <div className={`w-full  ${className}`}>
      {label && (
        <label className="block text-sm font-medium text-[#414651]">
          {label} {required && <span className="text-[#D92D20]">*</span>}
        </label>
      )}
      <div className="mt-1 flex gap-4">
        {options.map((option) => (
          <button
            key={option.value}
            type="button"
            onClick={() => onChange(option.value)}
            className={`px-4 py-[10px] rounded-lg border-2 transition-colors text-normal relative flex items-center w-1/2 ${
              value === option.value
                ? "bg-white border-[#45BF76] text-[#414651] justify-between max-[520px]:justify-center max-[520px]:gap-3"
                : "bg-white border-[#D5D7DA] text-[#414651] hover:border-[#45BF76] justify-center"
            }`}
          >
            {value === option.value && (
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 20 20"
                fill="none"
              >
                <path
                  d="M16.6666 5L7.49992 14.1667L3.33325 10"
                  stroke="#299D55"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            )}
            <span>{option.label}</span>
          </button>
        ))}
      </div>
      {error && helperText && (
        <p className="mt-1 text-sm text-[#D92D20]">{helperText}</p>
      )}
    </div>
  );
};

export default RadioGroup;
