// components/icons/ArrowLeftIcon.tsx
import React from "react";
import {CommonUtil} from "@/utils/CommonUtil";

const ArrowLeftIcon = ({ width = 20, height = 20, stroke = "#535862" }) =>
{
    width = Number(width) || 20;
    height = Number(height) || 20;
    let viewBox = `0 0 ${width} ${height}`;
    let strokeWidth = CommonUtil.getStrokeWidth({width: width,height: height});
    return  (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width={width}
            height={height}
            viewBox={viewBox}
            fill="none"
        >
            <path
                d="M15.8334 10H4.16675M4.16675 10L10.0001 15.8333M4.16675 10L10.0001 4.16666"
                stroke={stroke}
                strokeWidth={strokeWidth}
                strokeLinecap="round"
                strokeLinejoin="round"
            />
        </svg>
    )
};

export default ArrowLeftIcon;
