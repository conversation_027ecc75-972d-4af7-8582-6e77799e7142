import React from 'react';
import {CommonUtil} from "@/utils/CommonUtil";

const ChevronDownIcon = ({width = 20 , height = 20, stroke = '#535862'}) => {
    width = Number(width) || 20;
    height = Number(height) || 20;
    let viewBox = `0 0 ${width} ${height}`;
    let strokeWidth = CommonUtil.getStrokeWidth({width: width,height: height});
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width={width}
            height={height}
            viewBox={viewBox}
            fill="none"
        >
            <path
                d="M5 7.5L10 12.5L15 7.5"
                stroke={stroke}
                strokeWidth={strokeWidth}
                strokeLinecap="round"
                strokeLinejoin="round"
            />
        </svg>
    );
};

export default ChevronDownIcon;
