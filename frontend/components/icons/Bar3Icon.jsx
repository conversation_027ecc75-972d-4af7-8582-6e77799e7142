import React from 'react';
import {CommonUtil} from "@/utils/CommonUtil";

const Bar3Icon = ({
                                 width = 20,
                                 height = 20,
                                 stroke = '#535862',
                                 className = '',
                             }) => {
    width = Number(width) || 20;
    height = Number(height) || 20;
    let viewBox = `0 0 ${width} ${height}`;
    let strokeWidth = CommonUtil.getStrokeWidth({width: width,height: height});
    return (
        <div className={className}>
            <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox={viewBox}
                strokeWidth={strokeWidth}
                stroke={stroke}
                width={width}
                height={height}
                className={className}
            >
                <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M3.75 5.25h16.5m-16.5 4.5h16.5m-16.5 4.5h16.5m-16.5 4.5h16.5"
                />
            </svg>
        </div>

    );
};

export default Bar3Icon;
