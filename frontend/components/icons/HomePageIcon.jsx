import React from 'react';
import {CommonUtil} from "@/utils/CommonUtil";

const HomePageIcon = ({width = 20, height = 20, isActive = false}) => {
    width = Number(width) || 20;
    height = Number(height) || 20;

    let strokeWidth = CommonUtil.getStrokeWidth({width: width,height: height});
    let viewBox = `0 0 ${width} ${height}`;
    if (!isActive) {
        return (
            <svg
                xmlns="http://www.w3.org/2000/svg"
                width={width}
                height={height}
                viewBox={viewBox}
                fill="none"
                className="transition-colors duration-200"
            >
                <path
                    d="M6.66667 14.1663H13.3333M9.18141 2.30297L3.52949 6.6989C3.15168 6.99275 2.96278 7.13968 2.82669 7.32368C2.70614 7.48667 2.61633 7.67029 2.56169 7.86551C2.5 8.0859 2.5 8.32521 2.5 8.80384V14.833C2.5 15.7664 2.5 16.2331 2.68166 16.5896C2.84144 16.9032 3.09641 17.1582 3.41002 17.318C3.76654 17.4996 4.23325 17.4996 5.16667 17.4996H14.8333C15.7668 17.4996 16.2335 17.4996 16.59 17.318C16.9036 17.1582 17.1586 16.9032 17.3183 16.5896C17.5 16.2331 17.5 15.7664 17.5 14.833V8.80384C17.5 8.32521 17.5 8.0859 17.4383 7.86551C17.3837 7.67029 17.2939 7.48667 17.1733 7.32368C17.0372 7.13968 16.8483 6.99275 16.4705 6.69891L10.8186 2.30297C10.5258 2.07526 10.3794 1.9614 10.2178 1.91763C10.0752 1.87902 9.92484 1.87902 9.78221 1.91763C9.62057 1.9614 9.47418 2.07526 9.18141 2.30297Z"
                    stroke="currentColor"
                    strokeWidth={strokeWidth}
                    strokeLinecap="round"
                    strokeLinejoin="round"
                />
            </svg>
        );
    }else {
        return (
            <svg
                xmlns="http://www.w3.org/2000/svg"
                width={width}
                height={height}
                viewBox={viewBox}
                fill="#299D55"
            >
                <path
                    d="M9.6721 1.08898C9.88856 1.04553 10.1116 1.04554 10.328 1.08898L10.4354 1.11421L10.5689 1.15815C10.8724 1.27492 11.1277 1.48863 11.3306 1.64643L16.9825 6.04259L17.2291 6.23546C17.4675 6.4243 17.6784 6.60637 17.8435 6.82954L17.9704 7.01834C18.0883 7.21246 18.1792 7.42236 18.2406 7.64171L18.2732 7.77681C18.3385 8.09306 18.3334 8.42745 18.3334 8.80464V14.8341C18.3334 15.2867 18.3343 15.6767 18.3082 15.9962C18.2846 16.2842 18.2361 16.5717 18.1169 16.8499L18.0608 16.9687C17.851 17.3802 17.5319 17.7247 17.1404 17.9648L16.9686 18.0616C16.6535 18.2222 16.3254 18.2813 15.9962 18.3082C15.6765 18.3343 15.2861 18.3343 14.8332 18.3343H5.16689C4.71397 18.3343 4.32367 18.3343 4.00397 18.3082C3.71572 18.2846 3.42872 18.2364 3.15029 18.117L3.03147 18.0616C2.61999 17.8519 2.27554 17.5327 2.03538 17.1412L1.93935 16.9687C1.77882 16.6536 1.71888 16.3255 1.69196 15.9962C1.67893 15.8366 1.67309 15.6592 1.66998 15.4656L1.66673 14.8341V8.80464C1.66672 8.37348 1.65986 7.99797 1.7595 7.64171L1.8303 7.42606C1.91117 7.2137 2.02114 7.01283 2.15664 6.82954L2.2429 6.72049C2.45237 6.47476 2.71997 6.27411 3.01764 6.04259L8.66949 1.64643L8.85667 1.50076C9.0558 1.34889 9.29277 1.18787 9.56468 1.11421L9.6721 1.08898ZM6.66673 13.3334C6.20655 13.3335 5.83341 13.7066 5.83339 14.1668C5.83339 14.627 6.20654 15.0001 6.66673 15.0001H13.3334C13.7936 15.0001 14.1667 14.627 14.1667 14.1668C14.1667 13.7066 13.7936 13.3334 13.3334 13.3334H6.66673Z"
                    fill="#299D55"
                    strokeWidth={strokeWidth}
                />
            </svg>
        )
    }

};

export default HomePageIcon;


