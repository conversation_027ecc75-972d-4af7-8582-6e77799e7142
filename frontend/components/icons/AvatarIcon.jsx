// ExamIcon.jsx
import React from 'react';
import {CommonUtil} from "@/utils/CommonUtil";

const AvatarIcon = ({width = 24, height = 24, isActive = false}) => {
    width = Number(width) || 24;
    height = Number(height) || 24;
    let strokeWidth = CommonUtil.getStrokeWidth({width: width,height: height});
    let viewBox = `0 0 ${width} ${height}`;


    if (!isActive) {
        return (
            <svg xmlns="http://www.w3.org/2000/svg" width={width} height={height} viewBox={viewBox} fill="none">
                <path d="M5.40028 19.4384C6.00861 18.0052 7.42891 17 9.08398 17H15.084C16.7391 17 18.1594 18.0052 18.7677 19.4384M16.084 9.5C16.084 11.7091 14.2931 13.5 12.084 13.5C9.87485 13.5 8.08398 11.7091 8.08398 9.5C8.08398 7.29086 9.87485 5.5 12.084 5.5C14.2931 5.5 16.084 7.29086 16.084 9.5ZM22.084 12C22.084 17.5228 17.6068 22 12.084 22C6.56114 22 2.08398 17.5228 2.08398 12C2.08398 6.47715 6.56114 2 12.084 2C17.6068 2 22.084 6.47715 22.084 12Z"
                      stroke="#717680" strokeWidth={strokeWidth} strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
        );
    }else {
        return (
            <svg xmlns="http://www.w3.org/2000/svg" width={width} height={height} viewBox={viewBox} fill="none">
                <path fillRule="evenodd" clipRule="evenodd" d="M12.25 1C18.3251 1 23.25 5.92487 23.25 12C23.25 12.3661 23.2313 12.728 23.1963 13.085C23.1942 13.1065 23.1917 13.1279 23.1895 13.1494C23.1728 13.31 23.1524 13.4695 23.1289 13.6279C23.1247 13.6563 23.1197 13.6846 23.1152 13.7129C23.0908 13.8693 23.0632 14.0246 23.0322 14.1787C23.0275 14.2021 23.0234 14.2257 23.0186 14.249C22.9863 14.4043 22.9499 14.5581 22.9111 14.7109C22.9037 14.7403 22.8963 14.7696 22.8887 14.7988C22.8509 14.9428 22.811 15.0859 22.7676 15.2275C22.7544 15.2704 22.7402 15.3128 22.7266 15.3555C22.6846 15.4865 22.6424 15.6172 22.5957 15.7461C22.5845 15.7769 22.572 15.8072 22.5605 15.8379C22.5255 15.9318 22.4877 16.0245 22.4502 16.1172C22.4074 16.223 22.3623 16.3275 22.3164 16.4316C22.2852 16.5024 22.2533 16.5726 22.2207 16.6426C22.1769 16.7364 22.1322 16.8295 22.0859 16.9219C22.049 16.9956 22.0112 17.0688 21.9727 17.1416C21.9197 17.2415 21.8655 17.3404 21.8096 17.4385C21.7786 17.4927 21.7477 17.5469 21.7158 17.6006C21.644 17.7217 21.5694 17.8409 21.4932 17.959C21.4733 17.9897 21.4547 18.0212 21.4346 18.0518C21.3485 18.182 21.2583 18.3092 21.167 18.4355C21.1485 18.4611 21.131 18.4873 21.1123 18.5127C19.1097 21.2332 15.8871 23 12.25 23C8.80899 23 5.73776 21.4194 3.7207 18.9453C3.68758 18.9047 3.65657 18.8624 3.62402 18.8213C3.55087 18.7289 3.47744 18.6367 3.40723 18.542C3.35566 18.4724 3.30678 18.4009 3.25684 18.3301C3.19888 18.2479 3.1407 18.1659 3.08496 18.082C3.04427 18.0208 3.00531 17.9585 2.96582 17.8965C2.90643 17.8032 2.84764 17.7095 2.79102 17.6143C2.75166 17.5481 2.71378 17.4811 2.67578 17.4141C2.62466 17.3239 2.57401 17.2334 2.52539 17.1416C2.48574 17.0668 2.44812 16.9909 2.41016 16.915C2.36718 16.8292 2.32398 16.7434 2.2832 16.6562C2.24599 16.5767 2.21016 16.4965 2.1748 16.416C2.13099 16.3162 2.08876 16.2156 2.04785 16.1143C2.012 16.0255 1.97597 15.9366 1.94238 15.8467C1.92985 15.8131 1.91651 15.7798 1.9043 15.7461C1.85444 15.6084 1.80813 15.4692 1.76367 15.3291C1.75334 15.2965 1.74245 15.2641 1.73242 15.2314C1.68699 15.0834 1.64572 14.9338 1.60645 14.7832C1.60135 14.7636 1.59581 14.7442 1.59082 14.7246C1.55988 14.6032 1.52979 14.4813 1.50293 14.3584L1.47363 14.2168C1.47091 14.2035 1.4685 14.1901 1.46582 14.1768C1.39832 13.8405 1.34565 13.4989 1.30957 13.1523C1.3071 13.1286 1.30505 13.1048 1.30273 13.0811C1.26803 12.7254 1.25 12.3648 1.25 12C1.25 5.92487 6.17487 1 12.25 1ZM9.25 16C7.69476 16.0001 6.30622 16.7103 5.38965 17.8223C7.0405 19.7655 9.50041 21 12.25 21C14.9994 21 17.4585 19.7653 19.1094 17.8223C18.1928 16.7105 16.8051 16 15.25 16H9.25ZM12.25 4.5C9.48878 4.50023 7.25 6.73872 7.25 9.5C7.25 12.2613 9.48878 14.4998 12.25 14.5C15.0114 14.4999 17.25 12.2614 17.25 9.5C17.25 6.73861 15.0114 4.50005 12.25 4.5Z" fill="#299D55"/>
            </svg>
        )
    }

};

export default AvatarIcon;
