import React from 'react';
import {CommonUtil} from "@/utils/CommonUtil";

const AchievementIcon = ({width = 20, height = 20, isActive = false}) => {
    width = Number(width) || 20;
    height = Number(height) || 20;

    let strokeWidth = CommonUtil.getStrokeWidth({width: width,height: height});
    let viewBox = `0 0 ${width} ${height}`;
    if (!isActive) {
        return (
            <svg
                xmlns="http://www.w3.org/2000/svg"
                width={width}
                height={height}
                viewBox={viewBox}
                fill="none"
                className="transition-colors duration-200"
            >
                <g clipPath="url(#clip0_1625_907)">
                    <path
                        d="M10.0013 12.5013C7.23988 12.5013 5.0013 10.2627 5.0013 7.5013V2.87167C5.0013 2.5268 5.0013 2.35436 5.05156 2.21629C5.13581 1.98481 5.31815 1.80247 5.54962 1.71822C5.6877 1.66797 5.86013 1.66797 6.20501 1.66797H13.7976C14.1425 1.66797 14.3149 1.66797 14.453 1.71822C14.6845 1.80247 14.8668 1.98481 14.951 2.21629C15.0013 2.35436 15.0013 2.5268 15.0013 2.87167V7.5013C15.0013 10.2627 12.7627 12.5013 10.0013 12.5013ZM10.0013 12.5013V15.0013M15.0013 3.33464H17.0846C17.4729 3.33464 17.6671 3.33464 17.8202 3.39807C18.0244 3.48265 18.1866 3.64488 18.2712 3.84907C18.3346 4.00221 18.3346 4.19635 18.3346 4.58464V5.0013C18.3346 5.77628 18.3346 6.16377 18.2495 6.48168C18.0183 7.34441 17.3444 8.01828 16.4817 8.24945C16.1638 8.33464 15.7763 8.33464 15.0013 8.33464M5.0013 3.33464H2.91797C2.52968 3.33464 2.33554 3.33464 2.1824 3.39807C1.97821 3.48265 1.81598 3.64488 1.7314 3.84907C1.66797 4.00221 1.66797 4.19635 1.66797 4.58464V5.0013C1.66797 5.77628 1.66797 6.16377 1.75315 6.48168C1.98432 7.34441 2.65819 8.01828 3.52092 8.24945C3.83884 8.33464 4.22633 8.33464 5.0013 8.33464M6.20501 18.3346H13.7976C14.0021 18.3346 14.168 18.1688 14.168 17.9643C14.168 16.3279 12.8414 15.0013 11.205 15.0013H8.7976C7.1612 15.0013 5.83464 16.3279 5.83464 17.9643C5.83464 18.1688 6.00046 18.3346 6.20501 18.3346Z"
                        stroke="currentColor"
                        strokeWidth={strokeWidth}
                        strokeLinecap="round"
                        strokeLinejoin="round"
                    />
                </g>
                <defs>
                    <clipPath id="clip0_1625_907">
                        <rect width={width} height={height} fill="white" />
                    </clipPath>
                </defs>
            </svg>
        );
    }
};

export default AchievementIcon;
