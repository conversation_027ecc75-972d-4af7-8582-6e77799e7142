import React from 'react';
import {CommonUtil} from "@/utils/CommonUtil";

const ArrowDownIcon = ({width = 20, height = 20}) => {
    width = Number(width) || 20;
    height = Number(height) || 20;

    let strokeWidth = CommonUtil.getStrokeWidth({width: width,height: height});
    let viewBox = `0 0 ${width} ${height}`;
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width={width}
            height={height}
            viewBox={viewBox}
            fill="none"
        >
            <path
                d="M17.5 17.5667H2.5M15 9.23332L10 14.2333M10 14.2333L5 9.23332M10 14.2333V2.56665"
                stroke="#414651"
                strokeWidth={strokeWidth}
                strokeLinecap="round"
                strokeLinejoin="round"
            />
        </svg>
    );
};

export default ArrowDownIcon;
