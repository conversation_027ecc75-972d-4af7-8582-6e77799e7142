"use client";

import React, { useState, useContext, useCallback } from "react";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { GoogleLogin } from "@react-oauth/google";
import { jwtDecode } from "jwt-decode";
import Button from "./Button";
import TextField from "./TextField";
import { UserContext } from "../context/UserProvider";

const LoginPopup = ({ isOpen, onClose, fromCheckout = false }) => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [rememberMe, setRememberMe] = useState(false);
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);
  const router = useRouter();
  const { user, login, loginWithGoogle } = useContext(UserContext);

  const redirectAfterLogin = useCallback(() => {
    const courseDataString = localStorage.getItem("coursedata");
    if (courseDataString) {
      try {
        const courseData = JSON.parse(courseDataString);
        if (courseData.slug && courseData.tier_type) {
          const courseParam = `${courseData.slug}-${courseData.tier_type}`;
          const url = `/thanh-toan?${courseParam}`;
          router.push(url);
          return;
        }
      } catch (err) {
        console.error("Lỗi khi đọc dữ liệu khóa học:", err);
      }
    }

    router.push("/");
  }, [router, user]);

  const handleLogin = useCallback(
    async (e) => {
      e.preventDefault();
      setError("");
      setLoading(true);

      try {
        const result = await login(email, password, rememberMe);

        if (result.success) {
          onClose();
          router.refresh(); // Refresh để cập nhật state từ cookie

          // Kiểm tra xem có cần điền thông tin bổ sung không
          if (result.needsProfileCompletion) {
            const courseDataString = localStorage.getItem("coursedata");
            if (courseDataString) {
              sessionStorage.setItem("redirectAfterProfile", "payment");
            }
            router.push("/thong-tin-ca-nhan");
          } else {
            // Nếu đã có đủ thông tin, chuyển hướng theo luồng thanh toán
            redirectAfterLogin();
          }
        } else {
          setError(result.error || "Đăng nhập thất bại. Vui lòng thử lại.");
        }
      } catch (error) {
        console.error("Lỗi khi đăng nhập:", error);
        setError("Đăng nhập thất bại. Vui lòng thử lại sau.");
      } finally {
        setLoading(false);
      }
    },
    [login, email, password, rememberMe, router, onClose, redirectAfterLogin]
  );

  const handleGoogleSuccess = async (credentialResponse) => {
    setError("");
    setLoading(true);

    try {
      const decoded = jwtDecode(credentialResponse.credential);

      const result = await loginWithGoogle({
        credential: credentialResponse.credential,
        email: decoded.email,
        sub: decoded.sub,
      });

      if (result.success) {
        onClose();
        router.refresh(); // Refresh để cập nhật state từ cookie

        // Kiểm tra xem người dùng có cần bổ sung thông tin không
        if (result.needsProfileCompletion) {
          const courseDataString = localStorage.getItem("coursedata");
          if (courseDataString) {
            sessionStorage.setItem("redirectAfterProfile", "payment");
          }
          router.push("/thong-tin-ca-nhan");
        } else {
          // Nếu đã có đủ thông tin, chuyển hướng theo luồng thanh toán
          redirectAfterLogin();
        }
      } else {
        setError(
          result.error || "Đăng nhập với Google thất bại. Vui lòng thử lại."
        );
      }
    } catch (err) {
      console.error("Google login error:", err);
      setError("Đăng nhập với Google thất bại. Vui lòng thử lại sau.");
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-[#0A0D12] bg-opacity-70 flex items-center justify-center z-50">
      <div className="bg-[#FFFFFF] rounded-lg w-full max-w-[400px] p-6 relative">
        <div className="flex justify-between items-center relative mb-5">
          <div className="absolute right-0">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="25"
              viewBox="0 0 24 25"
              fill="none"
              className="cursor-pointer"
              onClick={onClose}
            >
              <path
                d="M18 6.18311L6 18.1831M6 6.18311L18 18.1831"
                stroke="#A4A7AE"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </div>
          <div className="w-full flex justify-center">
            <Image
              src="/images/Logo.png"
              alt="Logo"
              width={87}
              height={87}
              priority
            />
          </div>
        </div>

        <div className="text-center mb-6">
          <h2 className="text-lg font-semibold text-[#181D27]">
            Đăng nhập tài khoản
          </h2>
          <p className="text-sm text-[#535862] font-normal">
            Bạn phải đăng ký hoặc đăng nhập tài khoản
            <br />
            để thanh toán khoá học này
          </p>
        </div>

        <div className="mt-8">
          <div className="flex justify-center">
            <GoogleLogin
              onSuccess={handleGoogleSuccess}
              onError={() => setError("Đăng nhập bằng Google thất bại")}
              useOneTap
              type="standard"
              theme="outline"
              size="large"
              text="continue_with"
              shape="rectangular"
              style={{ width: "100%" }}
            />
          </div>
        </div>

        <div className="relative my-6">
          <div className="relative flex justify-center">
            <span className="text-sm text-[#535862] font-normal">hoặc</span>
          </div>
        </div>

        <form onSubmit={handleLogin}>
          {error && (
            <div className="mb-6 bg-[#FEF3F2] text-sm text-[#414651] font-normal flex gap-4 p-4 rounded-xl">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 20 20"
                fill="none"
              >
                <g clipPath="url(#clip0_3814_10675)">
                  <path
                    d="M10.0001 6.66666V9.99999M10.0001 13.3333H10.0084M18.3334 9.99999C18.3334 14.6024 14.6025 18.3333 10.0001 18.3333C5.39771 18.3333 1.66675 14.6024 1.66675 9.99999C1.66675 5.39762 5.39771 1.66666 10.0001 1.66666C14.6025 1.66666 18.3334 5.39762 18.3334 9.99999Z"
                    stroke="#D92D20"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </g>
                <defs>
                  <clipPath id="clip0_3814_10675">
                    <rect width="20" height="20" fill="white" />
                  </clipPath>
                </defs>
              </svg>
              {error}
            </div>
          )}

          <div className="space-y-5">
            <div>
              <TextField
                id="email"
                name="email"
                type="email"
                label="Email"
                required
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Nhập Email của bạn"
                error={error ? true : false}
              />
            </div>

            <div>
              <TextField
                id="password"
                name="password"
                type="password"
                label="Mật khẩu"
                required
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Nhập mật khẩu"
                error={error ? true : false}
              />
            </div>

            <div className="flex items-center justify-between">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={rememberMe}
                  onChange={(e) => setRememberMe(e.target.checked)}
                  className="h-4 w-4 border border-[#D5D7DA] rounded bg-white focus:outline-none checked:bg-[#299D55] checked:border-[#299D55] accent-[#299D55]"
                />
                <span className="ml-2 text-sm text-[#414651] font-medium">
                  Nhớ mật khẩu
                </span>
              </label>
              <Link
                href="/quen-mat-khau"
                className="text-sm text-[#198C43] font-medium"
              >
                Quên mật khẩu
              </Link>
            </div>

            <Button
              variant="secondaryGray"
              type="submit"
              className={`w-full ${
                loading ? "opacity-70 cursor-not-allowed" : ""
              }`}
              disabled={loading || !email || !password}
            >
              {loading ? "Đang xử lý..." : "Đăng nhập"}
            </Button>
          </div>
        </form>

        <p className="mt-6 text-center text-sm text-[#535862] font-normal">
          Bạn chưa có tài khoản?{" "}
          <Link
            href="/dang-ky"
            className="text-sm text-[#198C43] font-semibold"
            onClick={onClose}
          >
            Đăng ký ngay
          </Link>
        </p>
      </div>
    </div>
  );
};

export default LoginPopup;
