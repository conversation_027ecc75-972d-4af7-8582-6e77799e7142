import React, { useState, useEffect, useRef } from "react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import strapi from "../app/api/strapi";

const GradeSelect = ({ onClose, onSelectGrade }) => {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [grades, setGrades] = useState([]);
  const [error, setError] = useState(null);
  const popupRef = useRef(null);

  useEffect(() => {
    const fetchGrades = async () => {
      try {
        const data = await strapi.grade.getGrade();

        setGrades(data.data); // Giả sử data.data là mảng các khối lớp
      } catch (err) {
        console.error("Lỗi khi lấy dữ liệu:", err);
        setError(err);
      } finally {
        setLoading(false);
      }
    };

    fetchGrades();
  }, []);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (popupRef.current && !popupRef.current.contains(event.target)) {
        onClose(); // Close popup if clicked outside
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [onClose]);

  const handleSelectGrade = (grade) => {
    const slug = grade.course.slug;
    const gradeId = grade.course.gradeId;

    if (slug) {
      router.push(`/khoa-hoc/${slug}`);
    } else {
      console.error("Không tìm thấy slug cho khóa học.");
    }

    onClose(); // Close the popup after selecting a grade
  };

  const handleItemClick = (event) => {
    event.stopPropagation(); // Prevent click event from bubbling up
    // Handle item selection logic here
  };

  return (
    <div
      ref={popupRef}
      className="fixed inset-0 bg-[#000000] bg-opacity-50 backdrop-blur-[8px] flex items-center justify-center z-50"
      onClick={onClose}
    >
      <div
        className="bg-[#FFFFFF] rounded-2xl w-full max-w-[400px] mx-4"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="relative p-6">
          {/* Close button */}
          <button
            onClick={onClose}
            className="absolute right-4 top-4 text-gray-400 hover:text-gray-600"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
            >
              <path
                d="M18 6L6 18M6 6L18 18"
                stroke="#A4A7AE"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </button>

          {/* Content */}
          <div className="flex flex-col items-center">
            <Image
              src="/images/mascotgradeselect.png"
              alt="Mascot"
              width={180}
              height={180}
              className="mb-6"
            />
            <h3 className="text-lg font-semibold text-[#181D27] mb-[20px]">
              Bạn tìm hiểu khoá học lớp mấy?
            </h3>

            {/* Grade buttons */}
            <div className="w-full space-y-[20px]">
              {loading ? (
                <div className="flex justify-center py-4">
                  <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-[#45BF76]"></div>
                </div>
              ) : error ? (
                <div className="text-center text-red-500">
                  Không thể tải dữ liệu. Vui lòng thử lại.
                </div>
              ) : (
                grades.map((grade) => (
                  <button
                    key={grade.id}
                    className="w-full flex justify-center items-center gap-2 py-4 px-[22px] text-lg text-[#414651] font-semibold border border-[#D5D7DA] rounded-[10px] hover:bg-[#F0FFF7]"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleSelectGrade(grade);
                    }}
                  >
                    {grade.title}
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                    >
                      <path
                        d="M5 12H19M19 12L12 5M19 12L12 19"
                        stroke="#414651"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  </button>
                ))
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GradeSelect;
