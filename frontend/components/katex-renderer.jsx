"use client"

import { useEffect, useRef, useState } from "react"

const KATEX_CSS_URL = "https://cdn.jsdelivr.net/npm/katex@0.16.9/dist/katex.min.css"
const KATEX_JS_URL = "https://cdn.jsdelivr.net/npm/katex@0.16.9/dist/katex.min.js"
const MHCHEM_JS_URL = "https://cdn.jsdelivr.net/npm/katex@0.16.9/dist/contrib/mhchem.min.js"

// To prevent multiple loading attempts if several components mount
let katexLoadingInitiated = false
let katexFullyLoaded = false


export default function KaTeXRenderer({ content, className = "" }) {
    if (!content) return null;
    const containerRef = useRef(null)
    const [isKatexReady, setIsKatexReady] = useState(katexFullyLoaded)
    const [errorLoading, setErrorLoading] = useState(null)
    useEffect(() => {
        if (katexFullyLoaded) {
            setIsKatexReady(true)
            return
        }

        if (katexLoadingInitiated) {
            const interval = setInterval(() => {
                if (katexFullyLoaded) {
                    setIsKatexReady(true)
                    clearInterval(interval)
                }
            }, 100)
            return () => clearInterval(interval)
        }

        katexLoadingInitiated = true

        if (!document.querySelector(`link[href="${KATEX_CSS_URL}"]`)) {
            const cssLink = document.createElement("link")
            cssLink.rel = "stylesheet"
            cssLink.href = KATEX_CSS_URL
            document.head.appendChild(cssLink)
        }

        const katexScript = document.createElement("script")
        katexScript.src = KATEX_JS_URL
        katexScript.async = true
        katexScript.onload = () => {
            const mhchemScript = document.createElement("script")
            mhchemScript.src = MHCHEM_JS_URL
            mhchemScript.async = true
            mhchemScript.onload = () => {
                katexFullyLoaded = true
                setIsKatexReady(true)
            }
            mhchemScript.onerror = () => {
                console.error("Failed to load KaTeX mhchem extension.")
                setErrorLoading("Failed to load KaTeX mhchem extension.")
                katexLoadingInitiated = false
            }
            document.head.appendChild(mhchemScript)
        }
        katexScript.onerror = () => {
            console.error("Failed to load KaTeX.")
            setErrorLoading("Failed to load KaTeX.")
            katexLoadingInitiated = false
        }
        document.head.appendChild(katexScript)
    }, [])

    useEffect(() => {
        if (!isKatexReady || !containerRef.current || !window.katex) {
            return
        }

        if (errorLoading) {
            containerRef.current.textContent = `Error loading KaTeX: ${errorLoading}. Cannot render math.`
            return
        }

        const container = containerRef.current
        container.innerHTML = ""
        const parts = content.split(/(\$\$[\s\S]*?\$\$|\$[\s\S]*?\$|\\ce\{[^}]*\})/g)

        parts.forEach((part) => {
            if (part.startsWith("$$") && part.endsWith("$$")) {
                const math = part.slice(2, -2)
                const span = document.createElement("div")
                try {
                    window.katex.render(math, span, {
                        displayMode: true,
                        throwOnError: false,
                        trust: true,
                    })
                } catch (e) {
                    console.error("KaTeX display render error:", e, "Content:", math)
                    span.textContent = part
                }
                container.appendChild(span)
            } else if (part.startsWith("$") && part.endsWith("$") && part.length > 2) {
                const math = part.slice(1, -1)
                const span = document.createElement("span")
                try {
                    window.katex.render(math, span, {
                        displayMode: false,
                        throwOnError: false,
                        trust: true,
                    })
                } catch (e) {
                    console.error("KaTeX inline render error:", e, "Content:", math)
                    span.textContent = part
                }
                container.appendChild(span)
            } else if (part.startsWith("\\ce{") && part.endsWith("}")) {
                const span = document.createElement("span")
                try {
                    window.katex.render(part, span, {
                        displayMode: false,
                        throwOnError: false,
                        trust: true,
                    })
                } catch (e) {
                    console.error("KaTeX \\ce render error:", e, "Content:", part)
                    span.textContent = part
                }
                container.appendChild(span)
            } else {
                const textParts = part.split(/(\*\*[^*]+\*\*|\*[^*]+\*)/g)
                textParts.forEach((textPart) => {
                    if (textPart.startsWith("**") && textPart.endsWith("**")) {
                        const strong = document.createElement("strong")
                        strong.textContent = textPart.slice(2, -2)
                        container.appendChild(strong)
                    } else if (textPart.startsWith("*") && textPart.endsWith("*")) {
                        const em = document.createElement("em")
                        em.textContent = textPart.slice(1, -1)
                        container.appendChild(em)
                    } else {
                        const lines = textPart.split("\n")
                        lines.forEach((line, index) => {
                            if (index > 0) {
                                container.appendChild(document.createElement("br"))
                            }
                            if (line.trim() || line) {
                                const textNode = document.createTextNode(line)
                                container.appendChild(textNode)
                            }
                        })
                    }
                })
            }
        })
    }, [content, isKatexReady, errorLoading])

    if (errorLoading) {
        return (
            <div ref={containerRef} className={className}>
                Error loading KaTeX: {errorLoading}.
            </div>
        )
    }
    if (!isKatexReady) {
        return (
            <div ref={containerRef} className={className}>
                Loading KaTeX...
            </div>
        )
    }

    return <div ref={containerRef} className={className}>
        <style jsx>{`
          .katex span{
            font-size: inherit !important;
          }
      `}</style>
    </div>
}
