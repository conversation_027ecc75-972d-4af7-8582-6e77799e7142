"use client";

import { useState } from "react";

const Radio = ({ name = "radio-group", selected = false, onChange }) => {
  return (
    <div className="mt-0.5">
      <div
        className={`w-4 h-4 rounded-full border ${
          selected ? "border-[#299D55] bg-[#299D55]" : "border-[#D5D7DA]"
        } flex items-center justify-center`}
      >
        {selected && <div className="w-2 h-2 rounded-full bg-[#fff]"></div>}
      </div>
    </div>
  );
};

export default Radio;
