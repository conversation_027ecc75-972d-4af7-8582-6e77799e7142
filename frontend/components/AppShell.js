"use client";

import { usePathname } from "next/navigation";
import { AuthProvider } from "../context/AuthContext";
import { NotificationProvider } from "../context/NotificationContext";
import { UserProvider } from "../context/UserProvider";
import { Providers } from "../app/providers"; // Giả sử providers.js nằm trong app
import Header from "./Header";
import Footer from "./Footer";

export default function AppShell({ children }) {
  const pathname = usePathname();

  const isDashboard =
    pathname === "/quan-ly" || pathname.startsWith("/quan-ly/");
  const isLogin = pathname === "/dang-nhap";
  const isSignUp = pathname === "/dang-ky";
  const isVerify = pathname === "/xac-thuc";
  const isForgotPassword = pathname === "/quen-mat-khau";
  const isUserForm = pathname === "/thong-tin-ca-nhan";
  const isCheckout = pathname === "/thanh-toan";
  const isCourse = pathname.startsWith("/khoa-hoc/");
  const isNotification = pathname === "/thong-bao";
  const isAccount = pathname === "/tai-khoan";
  const isInvoice = pathname === "/hoa-don";

  const showHeaderFooter =
    !isDashboard &&
    !isLogin &&
    !isSignUp &&
    !isVerify &&
    !isForgotPassword &&
    !isUserForm &&
    !isCheckout &&
    !isNotification &&
    !isAccount &&
    !isInvoice;

  return (
    <Providers>
      <UserProvider>
        <AuthProvider>
          <NotificationProvider>
            {showHeaderFooter && <Header />}
            {children}
            {showHeaderFooter && <Footer hideOnMobile={isCourse} />}
          </NotificationProvider>
        </AuthProvider>
      </UserProvider>
    </Providers>
  );
}