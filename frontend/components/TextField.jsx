import React, { useState } from "react";
import clsx from "clsx";

const TextField = ({
  label,
  value,
  onChange,
  placeholder,
  type = "text",
  error,
  helperText,
  disabled,
  required,
  className,
  onClear,
  ...props
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  const handleFocus = () => {
    setIsFocused(true);
  };

  const handleBlur = () => {
    setIsFocused(false);
  };

  // Hàm xử lý khi nhấn nút clear
  const handleClear = (e) => {
    e.preventDefault();
    e.stopPropagation();

    // Giữ trạng thái focus
    setIsFocused(true);

    // Gọi callback onClear nếu được cung cấp
    if (onClear) {
      onClear();
    } else if (onChange) {
      // Nếu không có onClear, sử dụng onChange với giá trị trống
      const fakeEvent = {
        target: {
          value: "",
          name: props.name || "",
        },
      };
      onChange(fakeEvent);
    }

    // Focus lại vào input sau khi clear
    if (props.id) {
      const inputElement = document.getElementById(props.id);
      if (inputElement) {
        inputElement.focus();
      }
    }
  };

  // Hàm xử lý khi nhấn nút hiển thị/ẩn mật khẩu
  const toggleShowPassword = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setShowPassword(!showPassword);
  };

  // Xác định type thực tế cho input
  const actualType = type === "password" && showPassword ? "text" : type;

  // Tính toán padding bên phải dựa vào số lượng icon
  const calculatePadding = () => {
    let paddingRight = "pr-[14px]"; // Padding mặc định

    if (type === "password") {
      // Có ít nhất icon password
      if (error && value && isFocused) {
        // Có 3 icon: clear, error, password
        paddingRight = "pr-[84px]";
      } else if (error || (value && isFocused)) {
        // Có 2 icon: (clear hoặc error) và password
        paddingRight = "pr-[54px]";
      } else {
        // Chỉ có icon password
        paddingRight = "pr-[34px]";
      }
    } else {
      // Không phải password
      if (error && value && isFocused) {
        // Có 2 icon: clear và error
        paddingRight = "pr-[54px]";
      } else if (error || (value && isFocused)) {
        // Chỉ có 1 icon: error hoặc clear
        paddingRight = "pr-[34px]";
      }
    }

    return paddingRight;
  };

  return (
    <div className={clsx("flex flex-col gap-[6px]", className)}>
      {label && (
        <label className="text-sm font-medium text-[#414651]">
          {label}
          {required && <span className="text-[#D92D20]"> *</span>}
        </label>
      )}
      <div className="relative">
        <input
          type={actualType}
          value={value}
          onChange={onChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          disabled={disabled}
          placeholder={placeholder}
          className={clsx(
            "w-full px-[14px] py-[10px] rounded-lg text-base font-normal transition-all duration-200",
            "border focus:outline-none",
            calculatePadding(),
            {
              // Các trạng thái bình thường
              "border-[#D5D7DA] hover:border-[#45BF76] hover:border-2":
                !error && !disabled && !isFocused,
              "border-[#45BF76] border-2": isFocused && !error && !disabled,
              "text-[#181D27] placeholder:text-[#717680]": !disabled,

              // Các trạng thái có lỗi
              "border-[#F04438] border-1 hover:border-2 ": error && !disabled,

              // Trạng thái disabled
              "bg-[#FAFAFA] text-[#A4A7AE] cursor-not-allowed": disabled,
            }
          )}
          {...props}
        />

        <div className="absolute top-1/2 right-3 -translate-y-1/2 flex items-center gap-2">
          {/* Clear button - X - bên trái - chỉ hiển thị khi có giá trị và đang focus */}
          {value && !disabled && (
            <button
              type="button"
              onClick={handleClear}
              className="text-[#717680] focus:outline-none cursor-pointer hover:text-[#414651] transition-colors"
              aria-label="Clear text"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 20 20"
                fill="none"
                className="pointer-events-none"
              >
                <path
                  d="M10.0001 1.66669C5.39696 1.66669 1.66675 5.39794 1.66675 10C1.66675 14.6021 5.39696 18.3334 10.0001 18.3334C14.6032 18.3334 18.3334 14.6021 18.3334 10C18.3334 5.39794 14.6032 1.66669 10.0001 1.66669ZM13.8615 12.3886C14.0569 12.5839 14.1666 12.8488 14.1666 13.125C14.1666 13.4012 14.0569 13.6662 13.8615 13.8615C13.6662 14.0568 13.4013 14.1665 13.1251 14.1665C12.8489 14.1665 12.5839 14.0568 12.3886 13.8615L10.0001 11.4729L7.61154 13.8615C7.51503 13.9585 7.40028 14.0356 7.27389 14.0881C7.1475 14.1407 7.01197 14.1678 6.87508 14.1678C6.7382 14.1678 6.60267 14.1407 6.47628 14.0881C6.34989 14.0356 6.23514 13.9585 6.13862 13.8615C5.94334 13.6661 5.83364 13.4012 5.83364 13.125C5.83364 12.8488 5.94334 12.5839 6.13862 12.3886L8.52717 10L6.13862 7.61148C5.9433 7.41616 5.83357 7.15125 5.83357 6.87502C5.83357 6.5988 5.9433 6.33388 6.13862 6.13856C6.33394 5.94324 6.59886 5.83351 6.87508 5.83351C7.15131 5.83351 7.41622 5.94324 7.61154 6.13856L10.0001 8.5271L12.3886 6.13856C12.5839 5.94324 12.8489 5.83351 13.1251 5.83351C13.4013 5.83351 13.6662 5.94324 13.8615 6.13856C14.0569 6.33388 14.1666 6.5988 14.1666 6.87502C14.1666 7.15125 14.0569 7.41616 13.8615 7.61148L11.473 10L13.8615 12.3886Z"
                  fill="currentColor"
                />
              </svg>
            </button>
          )}

          {/* Error icon - ! - ở giữa */}
          {error && !disabled && (
            <div className="text-[#F04438]">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 16 16"
                fill="none"
              >
                <g clipPath="url(#clip0_1006_13928)">
                  <path
                    d="M7.99992 5.33331V7.99998M7.99992 10.6666H8.00659M14.6666 7.99998C14.6666 11.6819 11.6818 14.6666 7.99992 14.6666C4.31802 14.6666 1.33325 11.6819 1.33325 7.99998C1.33325 4.31808 4.31802 1.33331 7.99992 1.33331C11.6818 1.33331 14.6666 4.31808 14.6666 7.99998Z"
                    stroke="#F04438"
                    strokeWidth="1.33333"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </g>
                <defs>
                  <clipPath id="clip0_1006_13928">
                    <rect width="16" height="16" fill="white" />
                  </clipPath>
                </defs>
              </svg>
            </div>
          )}

          {/* Password toggle button - eye - bên phải (xa nhất) */}
          {type === "password" && !disabled && (
            <button
              type="button"
              onClick={toggleShowPassword}
              className="text-[#717680] focus:outline-none hover:text-[#414651] transition-colors cursor-pointer"
              aria-label={showPassword ? "Ẩn mật khẩu" : "Hiển thị mật khẩu"}
            >
              {showPassword ? (
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  viewBox="0 0 20 20"
                  fill="none"
                  className="pointer-events-none"
                >
                  <path
                    d="M2.01677 10.5944C1.90328 10.4147 1.84654 10.3248 1.81477 10.1863C1.79091 10.0822 1.79091 9.918 1.81477 9.8139C1.84654 9.67532 1.90328 9.58547 2.01677 9.40577C2.95461 7.92078 5.74617 4.16675 10.0003 4.16675C14.2545 4.16675 17.0461 7.92078 17.9839 9.40577C18.0974 9.58547 18.1541 9.67532 18.1859 9.8139C18.2098 9.918 18.2098 10.0822 18.1859 10.1863C18.1541 10.3248 18.0974 10.4147 17.9839 10.5944C17.0461 12.0794 14.2545 15.8334 10.0003 15.8334C5.74617 15.8334 2.95461 12.0794 2.01677 10.5944Z"
                    stroke="currentColor"
                    strokeWidth="1.66667"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                  <path
                    d="M10.0003 12.5001C11.381 12.5001 12.5003 11.3808 12.5003 10.0001C12.5003 8.61937 11.381 7.50008 10.0003 7.50008C8.61962 7.50008 7.50034 8.61937 7.50034 10.0001C7.50034 11.3808 8.61962 12.5001 10.0003 12.5001Z"
                    stroke="currentColor"
                    strokeWidth="1.66667"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              ) : (
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="21"
                  viewBox="0 0 20 21"
                  fill="none"
                  className="pointer-events-none"
                >
                  <path
                    d="M8.95245 4.7436C9.29113 4.69353 9.64051 4.66667 10.0003 4.66667C14.2545 4.66667 17.0461 8.4207 17.9839 9.90569C18.0974 10.0854 18.1542 10.1753 18.1859 10.3139C18.2098 10.418 18.2098 10.5822 18.1859 10.6863C18.1541 10.8249 18.097 10.9154 17.9827 11.0963C17.7328 11.4918 17.3518 12.0476 16.8471 12.6504M5.6036 6.09586C3.80187 7.31808 2.57871 9.01615 2.01759 9.9044C1.90357 10.0849 1.84656 10.1751 1.81478 10.3137C1.79091 10.4178 1.7909 10.582 1.81476 10.6861C1.84652 10.8247 1.90328 10.9146 2.01678 11.0943C2.95462 12.5793 5.74618 16.3333 10.0003 16.3333C11.7157 16.3333 13.1932 15.723 14.4073 14.8972M2.50035 3L17.5003 18M8.23258 8.73223C7.78017 9.18464 7.50035 9.80964 7.50035 10.5C7.50035 11.8807 8.61963 13 10.0003 13C10.6907 13 11.3157 12.7202 11.7681 12.2678"
                    stroke="currentColor"
                    strokeWidth="1.66667"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              )}
            </button>
          )}
        </div>
      </div>

      {/* Thông báo helper text hoặc error */}
      {(helperText || error) && (
        <span
          className={clsx("text-sm", {
            "text-[#535862]": !error,
            "text-[#F04438]": error,
          })}
        >
          {error || helperText}
        </span>
      )}
    </div>
  );
};

export default TextField;
