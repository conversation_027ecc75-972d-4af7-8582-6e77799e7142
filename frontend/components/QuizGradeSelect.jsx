"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import Button from "./Button";
import strapi from "../app/api/strapi";

// Đ<PERSON>nh nghĩa các loại câu hỏi theo chủ đề
const TOPICS = {
  voCo: "<PERSON><PERSON> cơ",
  huu<PERSON>o: "Hữu cơ",
  thucTe: "Thực tế",
  bienThienEnthalpy: "Biến thiên Enthalpy",
  halogen: "Halogen",
  nguyenTu: "Nguyên tử",
  oxyHoaKhu: "Oxy hóa - khử",
  canBangHoaHoc: "Cân bằng hóa học",
};

// Phân tích kết quả để tìm điểm yếu
const analyzeResults = (answers, questions, availableTopics) => {
  const topicResults = {};

  // Khởi tạo kết quả cho từng chủ đề có trong câu hỏi
  Object.keys(availableTopics).forEach((topicKey) => {
    topicResults[topicKey] = { correct: 0, total: 0 };
  });

  // T<PERSON>h số câu đúng và tổng số câu cho từng chủ đề
  questions.forEach((question) => {
    const userAnswer = answers[question.id];
    const topic = question.topic;

    if (topicResults[topic]) {
      topicResults[topic].total += 1;
      if (userAnswer === question.correctAnswer) {
        topicResults[topic].correct += 1;
      }
    }
  });

  const topicPercentages = {};
  Object.entries(topicResults).forEach(([topic, result]) => {
    topicPercentages[topic] =
      result.total > 0 ? (result.correct / result.total) * 100 : 0;
  });

  // Tìm chủ đề yếu nhất
  let weakestTopics = [];
  let lowestPercentage = 100;

  Object.entries(topicPercentages).forEach(([topic, percentage]) => {
    if (percentage < lowestPercentage && topicResults[topic].total > 0) {
      lowestPercentage = percentage;
      weakestTopics = [topic];
    } else if (
      percentage === lowestPercentage &&
      topicResults[topic].total > 0
    ) {
      weakestTopics.push(topic);
    }
  });

  const totalCorrect = Object.values(topicResults).reduce(
    (sum, result) => sum + result.correct,
    0
  );
  const totalQuestions = questions.length;
  const overallPercentage = (totalCorrect / totalQuestions) * 100;

  return {
    topicResults,
    topicPercentages,
    weakestTopics,
    totalCorrect,
    totalQuestions,
    overallPercentage,
  };
};

export default function QuizGradeSelect({ onClose, onSectionScroll }) {
  const [step, setStep] = useState(1);
  const [selectedGrade, setSelectedGrade] = useState(null);
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [questions, setQuestions] = useState([]);
  const [answers, setAnswers] = useState({});
  const [analysisResults, setAnalysisResults] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [availableTopics, setAvailableTopics] = useState({});

  const fetchQuestions = async (grade) => {
    try {
      setLoading(true);
      const response = await strapi.quizQuestions.getQuizQuestionsByGrade(
        grade
      );

      const questionData = response || [];

      if (questionData.length > 0) {
        const formattedQuestions = questionData.map((item) => {
          const options = [item.A, item.B, item.C, item.D];
          return {
            id: item.id,
            question: item.question,
            options: options,
            correctAnswer: options[["A", "B", "C", "D"].indexOf(item.answer)],
            topic: item.topic,
          };
        });

        const topics = {};
        formattedQuestions.forEach((q) => {
          if (q.topic) topics[q.topic] = TOPICS[q.topic];
        });

        setAvailableTopics(topics);
        setQuestions(formattedQuestions);
      } else {
        setError("Không tìm thấy câu hỏi cho lớp này");
      }
    } catch (err) {
      setError("Có lỗi xảy ra khi lấy dữ liệu");
    } finally {
      setLoading(false);
    }
  };

  const handleGradeSelect = (grade) => {
    setSelectedGrade(grade);
    fetchQuestions(grade);
    setStep(2);
  };

  const handleAnswerSelect = (answer) => {
    setAnswers({
      ...answers,
      [questions[currentQuestion].id]: answer,
    });
  };

  const handleNextQuestion = () => {
    if (currentQuestion < questions.length - 1) {
      setCurrentQuestion(currentQuestion + 1);
    } else {
      const results = analyzeResults(answers, questions, availableTopics);
      setAnalysisResults(results);
      setStep(3);
    }
  };

  const getLevelFromPercentage = (percentage) => {
    if (percentage >= 80) return "Xuất sắc";
    if (percentage >= 60) return "Khá";
    if (percentage >= 40) return "Trung bình";
    return "Cần cải thiện nhiều";
  };

  const getColorFromPercentage = (percentage) => {
    if (percentage > 60) return "#299D55"; // Xanh
    if (percentage > 30) return "#F59E0B"; // Cam
    return "#D92D20"; // Đỏ (≤ 30%)
  };

  const handleCompleteAndScroll = () => {
    onClose();
    // Chờ modal đóng hoàn toàn trước khi scroll
    setTimeout(() => {
      onSectionScroll("learning-method-header");
    }, 150); // Thêm delay 150ms
  };

  return (
    <div className="fixed inset-0 bg-[#0A0D12] bg-opacity-70 backdrop-blur-[16px] flex items-center justify-center z-50">
      {step === 1 && (
        <div className="bg-[#FFFFFF] rounded-xl shadow-lg w-full max-w-[728px] m-4">
          <div className="relative">
            <div className="flex flex-col h-full">
              <div className="px-4 md:px-8 pt-8 overflow-y-auto max-h-[calc(100vh-180px)] md:max-h-none pr-2">
                <button
                  onClick={onClose}
                  className="absolute right-3 top-3 p-2 text-gray-400 hover:text-gray-600 z-10"
                >
                  <Image
                    src="/images/homepage/x-close.svg"
                    alt="Close"
                    width={24}
                    height={24}
                  />
                </button>

                <div className="flex flex-col items-center">
                  <div className="mb-6">
                    <Image
                      src="/images/homepage/logo.png"
                      alt="Logo"
                      width={130}
                      height={80}
                      className="object-cover"
                    />
                  </div>

                  <div className="text-center mb-4 md:mb-6">
                    <h2 className="text-[24px] md:text-[30px] font-semibold text-[#181D27] mb-2">
                      Kiểm tra năng lực Hoá học
                    </h2>
                    <p className="text-base md:mx-[64px] text-[#535862] max-w-[600px]">
                      Hãy cho chúng tôi biết bạn đang chuẩn bị vào học lớp mấy
                      để nhận bài kiểm tra phù hợp
                    </p>
                  </div>

                  <div className="w-full space-y-3 md:space-y-5 mt-4 mb-8">
                    {["lop10", "lop11", "lop12"].map((grade, index) => (
                      <button
                        key={grade}
                        className="w-full flex justify-center items-center gap-2 py-3 md:py-4 px-4 md:px-[22px] text-base md:text-lg text-[#414651] font-semibold border border-[#D5D7DA] rounded-[10px] hover:bg-[#F0FFF7]"
                        onClick={() => handleGradeSelect(grade)}
                      >
                        {`Lớp ${10 + index}`}
                        <Image
                          src="/images/homepage/arrow-right.svg"
                          alt="Arrow right"
                          width={24}
                          height={24}
                        />
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {step === 2 && (
        <div className="bg-[#fff] rounded-xl shadow-lg w-full max-w-[728px] m-4">
          <div className="relative">
            <div className="flex flex-col h-full">
              <div className="px-4 md:px-8 pt-8 overflow-y-auto max-h-[calc(100vh-180px)] md:max-h-none pr-2">
                <button
                  onClick={onClose}
                  className="absolute right-3 top-3 p-2 text-gray-400 hover:text-gray-600 z-10"
                >
                  <Image
                    src="/images/homepage/x-close.svg"
                    alt="Close"
                    width={24}
                    height={24}
                  />
                </button>

                <div className="text-center mb-6">
                  <h2 className="text-[24px] md:text-[30px] font-semibold text-[#181D27] mb-2">
                    Kiểm tra năng lực Hoá học
                  </h2>
                  <p className="text-sm md:text-base text-[#535862] max-w-[600px] mx-auto">
                    Hãy cho chúng tôi biết bạn đang chuẩn bị vào học lớp mấy để
                    nhận bài kiểm tra phù hợp
                  </p>
                </div>

                {loading && (
                  <div className="flex flex-col items-center justify-center py-10">
                    <div className="w-10 h-10 border-4 border-[#299D55] border-t-transparent rounded-full animate-spin mb-4"></div>
                    <p className="text-base text-[#414651]">
                      Đang tải câu hỏi...
                    </p>
                  </div>
                )}

                {error && (
                  <div className="flex flex-col items-center justify-center py-10">
                    <div className="text-[#D92D20] mb-4">
                      <svg
                        width="40"
                        height="40"
                        viewBox="0 0 20 20"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M10 6.66675V10.0001M10 13.3334H10.0084M18.3334 10.0001C18.3334 14.6025 14.6024 18.3334 10 18.3334C5.39765 18.3334 1.66669 14.6025 1.66669 10.0001C1.66669 5.39771 5.39765 1.66675 10 1.66675C14.6024 1.66675 18.3334 5.39771 18.3334 10.0001Z"
                          stroke="#D92D20"
                          strokeWidth="1.66667"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </svg>
                    </div>
                    <p className="text-base text-[#D92D20] font-medium mb-2">
                      {error}
                    </p>
                    <Button
                      variant="secondary"
                      className="mt-2"
                      onClick={() => {
                        setError(null);
                        setStep(1);
                      }}
                    >
                      Quay lại
                    </Button>
                  </div>
                )}

                {!loading && !error && questions.length > 0 && (
                  <div className="flex flex-col gap-4 mb-8">
                    <div className="flex flex-col gap-2">
                      <div className="px-3 py-1 bg-[#F0FFF7] text-[#198C43] text-sm font-medium rounded-full border border-[#B5F2D7] inline-flex items-center w-fit">
                        Câu {currentQuestion + 1}/{questions.length}
                      </div>

                      <div className="max-w-full">
                        <h3 className="text-lg font-semibold text-[#181D27]">
                          {questions[currentQuestion]?.question}
                        </h3>
                      </div>
                    </div>

                    <div className="space-y-2 mt-2">
                      {questions[currentQuestion]?.options.map(
                        (option, index) => (
                          <label
                            key={index}
                            className={`p-3 rounded-lg border border-[#E9EAEB] flex items-start gap-3 cursor-pointer hover:bg-gray-50 ${
                              answers[questions[currentQuestion]?.id] === option
                                ? "border-[#299D55] bg-[#F0FFF7]"
                                : ""
                            }`}
                            onClick={() => handleAnswerSelect(option)}
                          >
                            <div className="mt-1">
                              <div
                                className={`w-5 h-5 rounded-full border cursor-pointer ${
                                  answers[questions[currentQuestion]?.id] ===
                                  option
                                    ? "border-[#299D55] bg-[#299D55]"
                                    : "border-[#D5D7DA]"
                                } flex items-center justify-center`}
                              >
                                {answers[questions[currentQuestion]?.id] ===
                                  option && (
                                  <div className="w-2 h-2 rounded-full bg-[#fff]"></div>
                                )}
                              </div>
                            </div>
                            <span className="text-base font-medium text-[#414651]">
                              {option}
                            </span>
                          </label>
                        )
                      )}
                    </div>
                  </div>
                )}
              </div>

              {/* Navigation */}
              <div className="bg-white px-4 md:px-8 py-6">
                <div className="flex justify-end">
                  <Button
                    variant="primary"
                    className="px-4 py-3"
                    onClick={handleNextQuestion}
                    disabled={!answers[questions[currentQuestion]?.id]}
                    icon={
                      !answers[questions[currentQuestion]?.id] ? (
                        <Image
                          src="/images/homepage/chevron-grey-right.svg"
                          alt="Chevron right"
                          width={20}
                          height={20}
                        />
                      ) : (
                        <Image
                          src="/images/homepage/chevron-right.svg"
                          alt="Chevron right"
                          width={20}
                          height={20}
                        />
                      )
                    }
                    iconPosition="right"
                  >
                    {currentQuestion === questions.length - 1
                      ? "Hoàn thành"
                      : "Tiếp theo"}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {step === 3 && analysisResults && (
        <div className="bg-[#fff] rounded-xl shadow-lg w-full max-w-[728px] m-4">
          <div className="relative">
            <div className="flex flex-col h-full">
              <div className="px-4 md:px-8 pt-8 overflow-y-auto max-h-[calc(100vh-180px)] md:max-h-none pr-2">
                <button
                  onClick={onClose}
                  className="absolute right-3 top-3 p-2 text-gray-400 hover:text-gray-600 z-10"
                >
                  <Image
                    src="/images/homepage/x-close.svg"
                    alt="Close"
                    width={24}
                    height={24}
                  />
                </button>

                <div className="mb-8">
                  <div className="text-center mb-8">
                    <h2 className="text-[24px] md:text-[30px] font-semibold text-[#181D27] mb-2">
                      Kết quả kiểm tra năng lực
                    </h2>
                    <p className="text-sm md:text-base text-[#535862]">
                      Dựa trên bài kiểm tra, chúng tôi đã phân tích trình độ Hóa
                      của bạn
                    </p>
                  </div>

                  <div className="flex flex-col items-center gap-2 mb-6">
                    <h3 className="text-base md:text-lg font-semibold text-[#181D27]">
                      Điểm số tổng quát
                    </h3>
                    <p
                      className="text-[36px] md:text-[48px] font-semibold leading-tight tracking-tighter"
                      style={{
                        color: getColorFromPercentage(
                          analysisResults.overallPercentage
                        ),
                      }}
                    >
                      {Math.round(analysisResults.overallPercentage)}%
                    </p>
                    <div
                      className="px-3 py-1 text-sm font-medium rounded-full"
                      style={{
                        backgroundColor: `${getColorFromPercentage(
                          analysisResults.overallPercentage
                        )}20`,
                        color: getColorFromPercentage(
                          analysisResults.overallPercentage
                        ),
                        borderColor: `${getColorFromPercentage(
                          analysisResults.overallPercentage
                        )}40`,
                        borderWidth: "1px",
                        borderStyle: "solid",
                      }}
                    >
                      {getLevelFromPercentage(
                        analysisResults.overallPercentage
                      )}
                    </div>
                    <p className="text-sm text-[#414651] font-medium">
                      Bạn đã trả lời đúng {analysisResults.totalCorrect}/
                      {analysisResults.totalQuestions} câu hỏi
                    </p>
                  </div>

                  {analysisResults.weakestTopics.length > 0 && (
                    <div
                      className="w-full p-2 rounded-xl mb-6 flex gap-2"
                      style={{
                        backgroundColor: `${getColorFromPercentage(
                          analysisResults.topicPercentages[
                            analysisResults.weakestTopics[0]
                          ]
                        )}20`,
                        borderColor: `${getColorFromPercentage(
                          analysisResults.topicPercentages[
                            analysisResults.weakestTopics[0]
                          ]
                        )}40`,
                        borderWidth: "1px",
                        borderStyle: "solid",
                      }}
                    >
                      <div className="mt-1">
                        <svg
                          width="20"
                          height="20"
                          viewBox="0 0 20 20"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M10 6.66675V10.0001M10 13.3334H10.0084M18.3334 10.0001C18.3334 14.6025 14.6024 18.3334 10 18.3334C5.39765 18.3334 1.66669 14.6025 1.66669 10.0001C1.66669 5.39771 5.39765 1.66675 10 1.66675C14.6024 1.66675 18.3334 5.39771 18.3334 10.0001Z"
                            stroke={getColorFromPercentage(
                              analysisResults.topicPercentages[
                                analysisResults.weakestTopics[0]
                              ]
                            )}
                            strokeWidth="1.66667"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </svg>
                      </div>
                      <div>
                        <h4
                          className="text-sm font-semibold"
                          style={{
                            color: getColorFromPercentage(
                              analysisResults.topicPercentages[
                                analysisResults.weakestTopics[0]
                              ]
                            ),
                          }}
                        >
                          Chủ đề cần cải thiện:
                        </h4>
                        <ul
                          className="text-base font-medium mt-1"
                          style={{
                            color: getColorFromPercentage(
                              analysisResults.topicPercentages[
                                analysisResults.weakestTopics[0]
                              ]
                            ),
                          }}
                        >
                          {analysisResults.weakestTopics.map((topic, index) => (
                            <li key={index}>
                              {TOPICS[topic]} (
                              {Math.round(
                                analysisResults.topicPercentages[topic]
                              )}
                              % đúng)
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  )}

                  <div className="w-full mb-6">
                    <h4 className="text-lg font-semibold text-[#181D27] mb-2">
                      Kết quả theo chủ đề:
                    </h4>
                    <div className="space-y-4">
                      {Object.entries(analysisResults.topicPercentages).map(
                        ([topic, percentage]) => (
                          <div key={topic} className="w-full">
                            <div className="flex justify-between mb-1">
                              <span className="text-sm font-medium text-[#414651]">
                                {TOPICS[topic]}
                              </span>
                              <span className="text-sm font-medium text-[#414651]">
                                {Math.round(percentage)}%
                              </span>
                            </div>
                            <div className="w-full h-2 bg-[#E9EAEB] rounded-full overflow-hidden">
                              <div
                                className="h-full rounded-full"
                                style={{
                                  width: `${percentage}%`,
                                  backgroundColor:
                                    getColorFromPercentage(percentage),
                                }}
                              />
                            </div>
                          </div>
                        )
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Footer with divider and button */}
              <div className="bg-white px-4 md:px-8">
                <div className="border-t border-[#E9EAEB] pt-6" />
                <div className="flex justify-center pb-6">
                  <Button
                    variant="primary"
                    className="px-4 py-3 w-[240px]"
                    onClick={handleCompleteAndScroll}
                  >
                    Xem phương pháp học
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
