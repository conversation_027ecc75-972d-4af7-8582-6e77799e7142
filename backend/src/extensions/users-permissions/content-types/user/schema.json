{"kind": "collectionType", "collectionName": "up_users", "info": {"name": "user", "description": "", "singularName": "user", "pluralName": "users", "displayName": "User"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"username": {"type": "string", "minLength": 3, "unique": true, "configurable": false, "required": true}, "email": {"type": "email", "minLength": 6, "configurable": false, "required": true}, "provider": {"type": "string", "configurable": false}, "password": {"type": "password", "minLength": 6, "configurable": false, "private": true, "searchable": false}, "resetPasswordToken": {"type": "string", "configurable": false, "private": true, "searchable": false}, "confirmationToken": {"type": "string", "configurable": false, "private": true, "searchable": false}, "confirmed": {"type": "boolean", "default": false, "configurable": false}, "blocked": {"type": "boolean", "default": false, "configurable": false}, "role": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.role", "inversedBy": "users", "configurable": false}, "image": {"type": "media", "multiple": true, "required": false, "allowedTypes": ["images", "files", "videos", "audios"]}, "gender": {"type": "boolean"}, "date": {"type": "date"}, "whereYouKnowWebsite": {"type": "string"}, "fullname": {"type": "string"}, "localpassword": {"type": "password"}, "googleToken": {"type": "text"}, "googleId": {"type": "string"}, "providers": {"type": "relation", "relation": "oneToMany", "target": "api::user-auth-provider.user-auth-provider", "mappedBy": "user"}, "orders": {"type": "relation", "relation": "oneToMany", "target": "api::order.order", "mappedBy": "users_permissions_user"}, "user_discord_id": {"type": "text"}, "phone": {"type": "string"}, "parents_phone": {"type": "string"}, "isUploadVideo": {"type": "boolean", "default": false}, "admin_notified": {"type": "enumeration", "enum": ["resolved", "notified"]}, "verified_otp": {"type": "boolean"}}}