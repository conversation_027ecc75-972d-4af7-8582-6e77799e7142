{"kind": "collectionType", "collectionName": "user_auth_providers", "info": {"singularName": "user-auth-provider", "pluralName": "user-auth-providers", "displayName": "user-auth-provider", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"user": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.user", "inversedBy": "providers"}, "provider": {"type": "string"}, "provider_id": {"type": "string"}}}