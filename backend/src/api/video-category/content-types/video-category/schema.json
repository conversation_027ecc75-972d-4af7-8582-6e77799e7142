{"kind": "collectionType", "collectionName": "video_categories", "info": {"singularName": "video-category", "pluralName": "video-categories", "displayName": "video_category", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"title": {"type": "string"}, "courses": {"type": "relation", "relation": "manyToMany", "target": "api::course.course", "inversedBy": "video_categories"}, "videos": {"type": "relation", "relation": "manyToMany", "target": "api::video.video", "mappedBy": "video_categories"}}}