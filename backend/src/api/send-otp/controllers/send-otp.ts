/**
 * A set of functions called "actions" for `send-otp`
 */

export default {
  async send(ctx) {
    try {
      const { email } = ctx.request.body;

      if (!email) {
        return ctx.badRequest('Email is required');
      }

      const result = await strapi.service('api::send-otp.send-otp').sendOTP(email);
      return ctx.send(result);
    } catch (err) {
      return ctx.badRequest(err.message);
    }
  },

  async verify(ctx) {
    try {
      const { email, code } = ctx.request.body;

      if (!email || !code) {
        return ctx.badRequest('Email and code are required');
      }

      const result = await strapi.service('api::send-otp.send-otp').verifyOTP(email, code);
      return ctx.send(result);
    } catch (err) {
      return ctx.badRequest(err.message);
    }
  },

  async sendPaymentConfirmation(ctx) {
    try {
      const { email, name, phone, orderId, paymentTime, paymentMethod, totalAmount, discount, courseName, courseFee, address, code } = ctx.request.body;

      if (!email || !name || !phone || !orderId || !paymentTime || !paymentMethod || !totalAmount || !courseName || !courseFee || !address || !code) {
        return ctx.badRequest('All fields are required');
      }

      const result = await strapi.service('api::send-otp.send-otp').sendPaymentConfirmationEmail(email, name, phone, orderId, paymentTime, paymentMethod, totalAmount, discount, courseName, courseFee, address, code);
      return ctx.send(result);
    } catch (err) {
      return ctx.badRequest(err.message);
    }
  }
};
