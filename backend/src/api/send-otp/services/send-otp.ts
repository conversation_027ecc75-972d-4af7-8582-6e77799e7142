/**
 * send-otp service
 */
import fs from "fs";
import path from "path";

export default () => ({
  async sendOTP(email: string) {
    const otp = Math.floor(100000 + Math.random() * 900000).toString();

    try {
      console.log("Attempting to send OTP to:", email);
      const templatePath = path.join(
        process.cwd(),
        "src/email-templates/OTP.html"
      );
      let emailTemplate = fs.readFileSync(templatePath, "utf8");

      emailTemplate = emailTemplate.replace(/{{otp}}/g, otp);

      const emailResult = await strapi.plugins["email"].services.email.send({
        to: email,
        from: "<<EMAIL>>",
        subject: "Ông Ba Dạy Hóa - OTP xác thực Email",
        html: emailTemplate,
      });

      console.log("Email send result:", emailResult);

      const otpRecord = await strapi.entityService.create("api::otp.otp", {
        data: {
          email,
          code: otp,
          expiresAt: new Date(Date.now() + 5 * 60 * 1000).toISOString(),
          verified: false,
        },
      });

      return { success: true, otp };
    } catch (error) {
      console.error("Failed to send OTP:", error);
      throw new Error(`Failed to send OTP: ${error.message}`);
    }
  },

  async sendPaymentConfirmationEmail(
    email: string,
    name: string,
    phone: string,
    orderId: string,
    paymentTime: string,
    paymentMethod: string,
    totalAmount: string,
    discount: string,
    courseName: string,
    courseFee: string,
    address: string,
    code: string
  ) {
    try {
      console.log("Attempting to send payment confirmation email to:", email);
      const templatePath = path.join(
        process.cwd(),
        "src/email-templates/Bill.html"
      );
      let emailTemplate = fs.readFileSync(templatePath, "utf8");

      // Thay thế thông tin trong template

      emailTemplate = emailTemplate.replace(/{{name}}/g, name);
      emailTemplate = emailTemplate.replace(/{{phone}}/g, phone);
      emailTemplate = emailTemplate.replace(/{{orderId}}/g, orderId);
      emailTemplate = emailTemplate.replace(/{{paymentTime}}/g, paymentTime);
      emailTemplate = emailTemplate.replace(
        /{{paymentMethod}}/g,
        paymentMethod
      );
      emailTemplate = emailTemplate.replace(/{{totalAmount}}/g, totalAmount);
      emailTemplate = emailTemplate.replace(/{{discount}}/g, discount);
      emailTemplate = emailTemplate.replace(/{{courseName}}/g, courseName);
      emailTemplate = emailTemplate.replace(/{{courseFee}}/g, courseFee);
      emailTemplate = emailTemplate.replace(/{{address}}/g, address);
      emailTemplate = emailTemplate.replace(/{{code}}/g, code);

      console.log("code", code);
      const emailResult = await strapi.plugins["email"].services.email.send({
        to: email,
        from: "<<EMAIL>>",
        subject: "Ông Ba Dạy Hóa - Hoá đơn thanh toán",
        html: emailTemplate,
      });

      console.log("Payment confirmation email sent:", emailResult);
      return { success: true, messageId: emailResult?.messageId };
    } catch (error) {
      console.error("Failed to send payment confirmation email:", error);
      throw new Error(
        `Failed to send payment confirmation email: ${error.message}`
      );
    }
  },

  async verifyOTP(email: string, code: string) {
    try {
      // Find the latest unverified OTP for this email
      const otpRecords = await strapi.entityService.findMany("api::otp.otp", {
        filters: {
          email,
          verified: false,
        },
        sort: { createdAt: "desc" },
        limit: 1,
      });

      if (!otpRecords || otpRecords.length === 0) {
        throw new Error("No OTP found for this email");
      }

      const otpRecord = otpRecords[0];

      // Check if OTP has expired
      if (new Date(otpRecord.expiresAt) < new Date()) {
        throw new Error("OTP has expired");
      }

      // Verify the OTP code
      if (otpRecord.code !== code) {
        throw new Error("Invalid OTP");
      }

      // Mark OTP as verified
      // await strapi.entityService.update('api::otp.otp', otpRecord.id, {
      //   data: {
      //     verified: true,
      //   },
      // });

      return {
        success: true,
        message: "OTP verified successfully",
      };
    } catch (error) {
      console.error("OTP verification error:", error);
      throw error;
    }
  },
});
