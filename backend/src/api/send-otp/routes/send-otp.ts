export default {
  routes: [
    {
      method: 'POST',
      path: '/send-otp',
      handler: 'send-otp.send',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'POST',
      path: '/verify-otp',
      handler: 'send-otp.verify',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'POST',
      path: '/send-payment-confirmation',
      handler: 'send-otp.sendPaymentConfirmation',
      config: {
        policies: [],
        middlewares: [],
      },
    },
  ],
};
