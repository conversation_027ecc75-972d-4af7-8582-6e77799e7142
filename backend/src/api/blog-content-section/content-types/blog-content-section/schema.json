{"kind": "collectionType", "collectionName": "blog_content_sections", "info": {"singularName": "blog-content-section", "pluralName": "blog-content-sections", "displayName": "Blog-Content-Section", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"title": {"type": "string"}, "content": {"type": "blocks"}, "image": {"type": "media", "multiple": true, "required": false, "allowedTypes": ["images", "files", "videos", "audios"]}, "imageCaption": {"type": "string"}, "order": {"type": "integer"}, "blogPost": {"type": "relation", "relation": "manyToOne", "target": "api::blog-post.blog-post", "inversedBy": "sections"}}}