/**
 * voucher router
 */

// Không cần import factories khi sử dụng cấu trúc routes trực tiếp

export default {
  routes: [
    // Các routes mặc định
    {
      method: "GET",
      path: "/vouchers",
      handler: "voucher.find",
      config: {
        auth: false,
      },
    },
    {
      method: "GET",
      path: "/vouchers/:id",
      handler: "voucher.findOne",
      config: {
        auth: false,
      },
    },
    // Custom routes
    {
      method: "POST",
      path: "/vouchers/validate",
      handler: "voucher.validateVoucher",
      config: {
        auth: false,
      },
    },
    {
      method: "POST",
      path: "/vouchers/increment-uses",
      handler: "voucher.incrementVoucherUses",
      config: {
        auth: false,
      },
    },
  ],
};
