/**
 * streak controller
 */

import {factories} from '@strapi/strapi'

export default factories.createCoreController('api::streak.streak', ({strapi}) => ({
    async rawTotal(ctx) {
        const user_id = 4;
        const rawQuery = `
            SELECT COUNT(*) as total
            FROM streaks
            WHERE user_id = ?
        `;
        const result = await strapi.db.connection.raw(rawQuery, [user_id]);
        ctx.body = {
            data: {total: result[0]?.total || 0},
            meta: {}
        };
    },
    async getStreakByUser(ctx) {
        const params = ctx.request.body;

        if (params && params.user_id) {
            const rawQuery = `WITH RECURSIVE week_days
                                                 AS (SELECT 0 AS day_offset, DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY) AS day
                              UNION ALL
                    SELECT day_offset + 1,
                           DATE_ADD(DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY), INTERVAL day_offset + 1 DAY)
                    FROM week_days
                    WHERE day_offset < 6 ),
                    min_date_streaks as (
                        select min(DATE(created_at)) min_created FROM streaks where 1=1 AND user_id = ?
                    ),
                    streak_data AS (
                      SELECT DATE(created_at) AS date_only, is_join, document_id, time
                      FROM streaks
                      WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY)
                       AND created_at < DATE_ADD(DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY), INTERVAL 7 DAY)
                       AND user_id = ?
                      )
                    select wd.day         AS dateInWeek,
                           sd.document_id AS documentId,
                           sd.time,
                           CASE DAYOFWEEK(wd.day)
                               WHEN 2 THEN 'T2'
                               WHEN 3 THEN 'T3'
                               WHEN 4 THEN 'T4'
                               WHEN 5 THEN 'T5'
                               WHEN 6 THEN 'T6'
                               WHEN 7 THEN 'T7'
                               WHEN 1 THEN 'CN'
                               END        AS name,
                           m.min_created,
                           case
                               when sd.is_join is not null then sd.is_join
                               when sd.document_id is null and wd.day >= m.min_created and wd.day >= CURDATE() then null
                               when sd.document_id is null and wd.day < m.min_created then null
                               when sd.document_id is null and wd.day >= m.min_created then 0
                               end           isJoin,
                           CASE
                               WHEN wd.day = CURDATE() THEN 1
                               ELSE 0
                               END        AS isActive
                    from week_days wd
                             LEFT JOIN streak_data sd ON DATE (sd.date_only) = wd.day
                        LEFT JOIN min_date_streaks m
                    ON TRUE
                    ORDER BY dateInWeek ASC`
            ;
            const result = await strapi.db.connection.raw(rawQuery, [params.user_id,params.user_id]);
            ctx.body = {
                data: result[0]
            }
        }
    },
    async getTotalRollup(ctx) {
        const params = ctx.request.body;
        if (params && params.user_id) {
            const rawQuery = `
                select DATE(created_at) dateJoin from streaks where user_id = ? and is_join = 1 
            `;

            const result = await strapi.db.connection.raw(rawQuery, [params.user_id]);
            const listStreak = result[0]; // mảng chứa các object { dateJoin: "YYYY-MM-DD" }
            let count = 0;
            let tmp = new Date();
            tmp.setHours(0, 0, 0, 0);
            let localDate = tmp.toLocaleDateString('en-CA');
            if (listStreak.some(item => item.dateJoin === localDate)) {
                count++;
            }
            while (true) {
                const yesterday = new Date(tmp);
                yesterday.setDate(yesterday.getDate() - 1);
                yesterday.setHours(0, 0, 0, 0);
                const formatted = yesterday.toLocaleDateString('en-CA');
                const found = listStreak.some(item => item.dateJoin === formatted);
                if (found) {
                    count++;
                    tmp = yesterday;
                } else {
                    break;
                }
            }
            ctx.body = {
                data: count
            }
        }
    },
    async getDataFinish(ctx) {
        const params = ctx.request.body;
        if (params && params.user_id && params.streak_question_id) {
            const rawQuery = `
                select sum(count) count, sum(is_correct) is_correct ,sum( case when is_star_point = 1 then nvl(point_,0) *2 else nvl(point_,0) end ) point, time,
                get_max_is_join_streak(?) max_streak
                from (
                    select
                    1 count, qa.*, qaq.question_id, (select a.point from exercise_types a where a.id = qet.exercise_type_id and qa.is_correct = 1) point_, (select a.time from streaks a where a.user_id = qau.user_id and a.streak_question_id = qa.streak_question_id) time
                    from questions_answers qa
                    inner join questions_answers_user_lnk qau on qa.id = qau.questions_answer_id
                    inner join questions_answers_question_lnk qaq on qa.id = qaq.questions_answer_id
                    inner join questions_exercise_type_lnk qet on qaq.question_id = qet.question_id
                    where qa.streak_question_id = ? and qau.user_id = ?
                    ) a
                group by time
            `;
            const result = await strapi.db.connection.raw(rawQuery, [ params.user_id,params.streak_question_id,params.user_id]);
            debugger
            ctx.body = {
                data: result[0]
            }
        }
    },
}));