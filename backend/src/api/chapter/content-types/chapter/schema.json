{"kind": "collectionType", "collectionName": "chapters", "info": {"singularName": "chapter", "pluralName": "chapters", "displayName": "Chapter", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"title": {"type": "string"}, "basicKnowlegdeCount": {"type": "integer"}, "exerciseCount": {"type": "integer"}, "order": {"type": "integer"}, "knowledgeSections": {"type": "relation", "relation": "oneToMany", "target": "api::knowledge-section.knowledge-section", "mappedBy": "chapter"}, "exercises": {"type": "relation", "relation": "oneToMany", "target": "api::exercise.exercise", "mappedBy": "chapter"}, "course": {"type": "relation", "relation": "manyToOne", "target": "api::course.course", "inversedBy": "chapters"}, "video_demo": {"type": "text"}}}