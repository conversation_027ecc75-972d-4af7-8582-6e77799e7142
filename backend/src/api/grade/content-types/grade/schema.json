{"kind": "collectionType", "collectionName": "grades", "info": {"singularName": "grade", "pluralName": "grades", "displayName": "Grade", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"title": {"type": "string"}, "course": {"type": "relation", "relation": "oneToOne", "target": "api::course.course", "inversedBy": "grade"}, "upload_videos": {"type": "relation", "relation": "manyToMany", "target": "api::upload-video.upload-video", "mappedBy": "grades"}}}