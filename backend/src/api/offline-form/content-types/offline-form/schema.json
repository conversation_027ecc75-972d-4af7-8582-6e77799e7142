{"kind": "collectionType", "collectionName": "offline_forms", "info": {"singularName": "offline-form", "pluralName": "offline-forms", "displayName": "Offline_form", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"name": {"type": "string"}, "yearofbirth": {"type": "string"}, "phone": {"type": "string"}, "email": {"type": "email"}, "address": {"type": "text"}, "grade": {"type": "string"}, "classname": {"type": "string"}, "school": {"type": "string"}, "status": {"type": "enumeration", "enum": ["pending", "approved", "rejected"], "default": "pending"}, "class_schedules": {"type": "relation", "relation": "manyToMany", "target": "api::class-schedule.class-schedule", "mappedBy": "offline_forms"}}}