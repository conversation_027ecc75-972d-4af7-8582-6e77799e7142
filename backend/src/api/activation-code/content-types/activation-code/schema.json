{"kind": "collectionType", "collectionName": "activation_codes", "info": {"singularName": "activation-code", "pluralName": "activation-codes", "displayName": "Activation_code", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"code": {"type": "string"}, "activation_status": {"type": "boolean"}, "activated_at": {"type": "datetime"}, "discord_status": {"type": "boolean"}, "orders": {"type": "relation", "relation": "manyToMany", "target": "api::order.order", "mappedBy": "activation_codes"}}}