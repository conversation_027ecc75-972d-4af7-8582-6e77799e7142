{"kind": "collectionType", "collectionName": "streak_questions", "info": {"singularName": "streak-question", "pluralName": "streak-questions", "displayName": "streak_question", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"description": {"type": "text"}, "value": {"type": "date"}, "course": {"type": "relation", "relation": "oneToOne", "target": "api::course.course"}}}