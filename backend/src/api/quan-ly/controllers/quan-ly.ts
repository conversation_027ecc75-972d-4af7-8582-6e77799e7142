export default {
    async getVideoUpload(ctx) {
        try {
            const body = ctx.request.body as any;
            if (body) {
                const collectionId = body.collectionId;
                const videos = await strapi.entityService.findMany('api::upload-video.upload-video', {
                    filters: { collection_id: collectionId },
                    sort: ['createdAt:desc'],
                });
                if (videos.length > 0) {
                    return ctx.send({
                        message: 'Tạo video thành công',
                        data: videos,
                        success: true
                    })
                }
                return ctx.send({
                    message: 'Không tìm thấy video upload nào',
                    data: [],
                    success: true
                })
            }
        } catch (err) {
            ctx.send({
                message: 'Có lỗi xảy ra: ' + err,
                success: false
            })
            ctx.status = 500;
        }
    },
};