{"kind": "collectionType", "collectionName": "features", "info": {"singularName": "feature", "pluralName": "features", "displayName": "Feature", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"icon": {"type": "text"}, "title": {"type": "string"}, "description": {"type": "text"}, "courses": {"type": "relation", "relation": "manyToMany", "target": "api::course.course", "inversedBy": "features"}}}