{"kind": "collectionType", "collectionName": "faqs", "info": {"singularName": "faq", "pluralName": "faqs", "displayName": "FAQ", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"question": {"type": "string"}, "order": {"type": "integer"}, "course": {"type": "relation", "relation": "manyToOne", "target": "api::course.course", "inversedBy": "faqs"}, "answer": {"type": "text"}}}