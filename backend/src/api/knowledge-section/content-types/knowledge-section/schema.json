{"kind": "collectionType", "collectionName": "knowledge_sections", "info": {"singularName": "knowledge-section", "pluralName": "knowledge-sections", "displayName": "KnowledgeSection"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"title": {"type": "string"}, "order": {"type": "integer"}, "items": {"type": "json"}, "chapter": {"type": "relation", "relation": "manyToOne", "target": "api::chapter.chapter", "inversedBy": "knowledgeSections"}}}