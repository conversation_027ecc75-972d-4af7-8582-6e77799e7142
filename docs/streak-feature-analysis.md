# Tài Liệu Phân Tích Tính Năng Streak - Ông Ba Dạy Hóa

## 📋 Tổng Quan Tính Năng

### Mục Đích
Tính năng Streak (chuỗi ngày học liên tiếp) được thiết kế để khuyến khích học sinh duy trì thói quen học tập hàng ngày thông qua việc trả lời câu hỏi hóa học với hệ thống điểm thưởng và theo dõi tiến độ.

### Cách Hoạt Động
- **Tần suất**: Mỗi ngày có một bộ câu hỏi streak mới
- **Cấu trúc**: 3 câu hỏi với độ khó tăng dần (2 câu cơ bản + 1 câu khó)
- **Điểm thưởng**: Hệ thống điểm dựa trên độ khó và star point (x2 điểm)
- **Theo dõ<PERSON>**: Tracking streak count và thời gian hoàn thành

## 🏗️ Cấu Trúc <PERSON>u Hỏi

### Phân Loại Theo Exercise Type
```javascript
// Exercise Types với điểm số tương ứng
{
  "hieu": "Hiểu biết cơ bản",      // Điểm thấp
  "biet": "Biết",                  // Điểm trung bình
  "van_dung": "Vận dụng",          // Điểm cao
  "van_dung_cao": "Vận dụng cao"   // Điểm cao nhất
}
```

### Cấu Trúc Bộ Câu Hỏi
1. **Câu 1**: Exercise type "biet" hoặc "hieu" (dễ)
2. **Câu 2**: Exercise type "biet" hoặc "hieu" (dễ)
3. **Câu 3**: Exercise type "van_dung" hoặc "van_dung_cao" (khó)

### Loại Câu Hỏi
- **TN_4**: Trắc nghiệm 4 đáp án (A, B, C, D)
- **TN_2**: Trắc nghiệm 2 đáp án
- **TN_value**: Trắc nghiệm điền giá trị

## 🔄 User Flow Chi Tiết

### 1. Khởi Tạo Streak Hàng Ngày
```javascript
// Flow bắt đầu streak
1. User truy cập /quan-ly/streak
2. System check streak_question cho ngày hiện tại
3. Nếu chưa có streak record → hiển thị button "Bắt đầu"
4. Nếu đã có → hiển thị trạng thái tương ứng
```

### 2. Quy Trình Trả Lời Câu Hỏi
```javascript
// Flow trả lời câu hỏi
Phase 1: Câu 1 & 2 (Câu dễ)
├── User chọn đáp án
├── Option: Sử dụng Star Point (x2 điểm, chỉ 1 lần/ngày)
├── Submit answer → Hiển thị kết quả
└── Next question

Phase 2: Lựa chọn tiếp tục
├── Sau câu 2 → Popup lựa chọn
├── Option 1: "Hoàn thành streak" (kết thúc)
└── Option 2: "Thử sức" (làm câu 3)

Phase 3: Câu 3 (Câu khó - Optional)
├── User chọn đáp án
├── Submit answer → Hiển thị kết quả
└── Hoàn thành streak
```

### 3. Tính Điểm và Hoàn Thành
```javascript
// Logic tính điểm
const calculatePoints = (exerciseType, isCorrect, isStarPoint) => {
  const basePoint = exerciseType.point;
  if (!isCorrect) return 0;
  return isStarPoint ? basePoint * 2 : basePoint;
}
```

## 🗄️ Database Schema

### Bảng `streaks`
```sql
CREATE TABLE streaks (
  id INT PRIMARY KEY AUTO_INCREMENT,
  document_id VARCHAR(255),
  name VARCHAR(255),
  user_id INT NOT NULL,
  isJoin BOOLEAN DEFAULT FALSE,  -- Trạng thái hoàn thành
  time INT,                      -- Thời gian hoàn thành (giây)
  streak_question_id INT,        -- Link đến bộ câu hỏi ngày
  created_at TIMESTAMP,
  updated_at TIMESTAMP
);
```

### Bảng `streak_questions`
```sql
CREATE TABLE streak_questions (
  id INT PRIMARY KEY AUTO_INCREMENT,
  document_id VARCHAR(255),
  description TEXT,
  value DATE,                    -- Ngày của streak
  course_id INT,                 -- Link đến khóa học
  created_at TIMESTAMP,
  updated_at TIMESTAMP
);
```

### Bảng `questions_answers`
```sql
CREATE TABLE questions_answers (
  id INT PRIMARY KEY AUTO_INCREMENT,
  document_id VARCHAR(255),
  answer VARCHAR(255),           -- Đáp án user chọn
  is_correct BOOLEAN,            -- Đúng/sai
  is_star_point BOOLEAN,         -- Có sử dụng star point
  user_id INT,                   -- User trả lời
  streak_question_id INT,        -- Link đến streak
  question_id INT,               -- Link đến câu hỏi
  created_at TIMESTAMP,
  updated_at TIMESTAMP
);
```

## 🔌 API Endpoints

### Core Streak APIs
```javascript
// 1. Lấy streak question theo ngày
GET /streak-questions?filters[value][$eq]=${date}&filters[course][id][$eq]=${courseId}

// 2. Tạo streak record mới
POST /streaks
Body: { user_id, streak_question_id, isJoin: 0 }

// 3. Lấy câu hỏi theo streak
GET /questions?filters[streak_question][id][$eq]=${streakId}&sort=exercise_type.point:asc

// 4. Lưu câu trả lời
POST /questions-answers
Body: { user, streak_question_id, answer, is_correct, is_star_point, question }

// 5. Cập nhật thời gian
PUT /streaks/${documentId}
Body: { time }

// 6. Hoàn thành streak
PUT /streaks/${documentId}
Body: { isJoin: true, time }
```

### Custom Analytics APIs
```javascript
// 7. Lấy dữ liệu streak theo user
POST /streaks/get-streak-by-user
Body: { user_id }

// 8. Tính tổng streak count
POST /streaks/get-total-rollup
Body: { user_id }

// 9. Lấy kết quả hoàn thành
POST /streaks/get-data-finish
Body: { user_id, streak_question_id }
```

## 🎨 Frontend Components

### Main Component: `/quan-ly/streak/page.jsx`
**State Management:**
```javascript
const [streakId, setStreakId] = useState(null);           // ID bộ câu hỏi
const [questions, setQuestions] = useState([]);           // Danh sách 3 câu hỏi
const [questionActive, setQuestionActive] = useState({}); // Câu hỏi hiện tại
const [indexQuestionActive, setIndexQuestionActive] = useState(0); // Index câu hỏi
const [userAnswer, setUserAnswer] = useState({});         // Câu trả lời user
const [isViewAnswer, setIsViewAnswer] = useState(false);  // Hiển thị đáp án
const [isQuestion2, setIsQuestion2] = useState(false);    // Popup lựa chọn sau câu 2
const [isStart, setIsStart] = useState(false);            // Trạng thái bắt đầu
const [isFinish, setIsFinish] = useState(false);          // Trạng thái hoàn thành
const [time, setTime] = useState(0);                      // Timer
const [isUseStarPoint, setIsUseStarPoint] = useState(false); // Đã dùng star point
```

### UI Components
1. **StarPointPopup**: Xác nhận sử dụng star point (x2 điểm)
2. **OutStreakPopup**: Xác nhận thoát streak
3. **ContinueStreak**: Popup tiếp tục streak đã bắt đầu
4. **TutorialStreak**: Hướng dẫn sử dụng streak
5. **ReportStreak**: Báo cáo lỗi câu hỏi

## ⚙️ Business Logic

### Streak Initialization
```javascript
const clickStart = async () => {
  // Tạo streak record với isJoin: 0
  const data = {
    isJoin: 0,
    user_id: user.id,
    streak_question_id: streakId
  };
  const res = await strapi.streak.createStreak(data);
  // Bắt đầu timer và load câu hỏi đầu tiên
};
```

### Question Flow Control
```javascript
const ViewAnswerAndNextQuestion = async () => {
  if (!isViewAnswer) {
    // Lưu câu trả lời
    await strapi.streak.saveQuestionAnswer(userAnswer);
    setIsViewAnswer(true);
  } else {
    const nextIndex = indexQuestionActive + 1;
    if (nextIndex === 2) {
      setIsQuestion2(true); // Hiển thị popup lựa chọn
    } else if (nextIndex === 3) {
      // Hoàn thành sau câu 3
      await strapi.streak.updateTime({documentId, time});
    } else {
      // Chuyển câu tiếp theo
      setIndexQuestionActive(nextIndex);
    }
  }
};
```

### Star Point System
```javascript
const useStarPoint = () => {
  if (userAnswer.is_star_point) {
    // Hủy star point
    setUserAnswer({...userAnswer, is_star_point: false});
  } else {
    // Hiển thị popup xác nhận
    setIsViewStarPoint(true);
  }
};
```

## 📊 Streak Analytics

### Weekly Progress Tracking
```sql
-- Query lấy dữ liệu tuần
SELECT 
  wd.day AS dateInWeek,
  sd.document_id AS documentId,
  sd.time,
  CASE DAYOFWEEK(wd.day)
    WHEN 2 THEN 'T2' WHEN 3 THEN 'T3'
    WHEN 4 THEN 'T4' WHEN 5 THEN 'T5'
    WHEN 6 THEN 'T6' WHEN 7 THEN 'T7'
    WHEN 1 THEN 'CN'
  END AS name,
  CASE 
    WHEN sd.is_join IS NOT NULL THEN sd.is_join
    WHEN wd.day = CURDATE() THEN NULL
    ELSE 0
  END AS isJoin
FROM week_days wd
LEFT JOIN streak_data sd ON DATE(sd.date_only) = wd.day
WHERE sd.user_id = ?
ORDER BY dateInWeek ASC;
```

### Streak Count Calculation
```javascript
const calculateStreakCount = (streakDates) => {
  let count = 0;
  let currentDate = new Date();
  
  // Kiểm tra từ hôm nay trở về trước
  while (streakDates.includes(formatDate(currentDate))) {
    count++;
    currentDate.setDate(currentDate.getDate() - 1);
  }
  
  return count;
};
```

## 🚨 Use Cases và Edge Cases

### Normal Use Cases
1. **Hoàn thành đầy đủ**: User làm cả 3 câu và hoàn thành
2. **Hoàn thành sớm**: User làm 2 câu và chọn "Hoàn thành streak"
3. **Sử dụng Star Point**: User dùng star point cho 1 câu để x2 điểm
4. **Tiếp tục streak**: User quay lại tiếp tục streak đã bắt đầu

### Edge Cases
1. **Không có streak question**: Ngày chưa được tạo streak question
2. **Đã hoàn thành**: User đã hoàn thành streak ngày hôm đó
3. **Mất kết nối**: User mất kết nối giữa chừng
4. **Timeout**: User để quá lâu không làm
5. **Câu hỏi lỗi**: Câu hỏi thiếu thông tin hoặc sai đáp án

### Error Scenarios
```javascript
// 1. Không tìm thấy streak question
if (!streakQuestion) {
  showNotification({
    type: "error",
    title: "Streak không tìm thấy",
    message: "Nhắn cho Ba để thêm thông tin streak nhé bây!"
  });
}

// 2. Chưa chọn đáp án
if (!userAnswer.answer) {
  toast.error('Điền đáp án đã nhé!');
  return;
}

// 3. API call failed
catch (error) {
  console.log('Streak API Error:', error);
  // Handle gracefully
}
```

## ⚠️ Điểm Cần Cải Thiện

### Hardcoded Values
1. **Timer logic**: Hardcode 3600 seconds timeout
2. **Progress calculation**: Fixed 100/3 for progress bar
3. **Ranking display**: Hardcode "51/100" ranking
4. **Sample data**: Hardcode sample questions in getData()

### Performance Issues
1. **Multiple API calls**: Không optimize batch requests
2. **Real-time updates**: Thiếu real-time sync
3. **Caching**: Không cache câu hỏi đã load
4. **Database queries**: Raw SQL queries không optimize

### UX Problems
1. **Loading states**: Thiếu loading indicators
2. **Error handling**: Error messages không user-friendly
3. **Responsive design**: Một số components chưa responsive tốt
4. **Accessibility**: Thiếu ARIA labels và keyboard navigation

### Technical Debt
1. **State management**: Quá nhiều useState, cần refactor
2. **Component size**: Main component quá lớn (900+ lines)
3. **Code duplication**: Logic duplicate giữa các functions
4. **Type safety**: Thiếu TypeScript types

## 🚀 Recommendations

### Immediate Improvements
1. **Config hóa hardcoded values**
   ```javascript
   const STREAK_CONFIG = {
     TIMER_DURATION: 3600,
     QUESTIONS_PER_STREAK: 3,
     STAR_POINT_MULTIPLIER: 2
   };
   ```

2. **Optimize API calls**
   ```javascript
   // Batch multiple requests
   const [questions, userProgress, streakData] = await Promise.all([
     getQuestionByStreak(streakId),
     getUserProgress(userId),
     getStreakData(userId)
   ]);
   ```

3. **Improve error handling**
   ```javascript
   const handleApiError = (error, fallbackMessage) => {
     const message = error.response?.data?.message || fallbackMessage;
     showNotification({ type: "error", message });
   };
   ```

### Long-term Enhancements
1. **Real-time features**: WebSocket cho live updates
2. **Offline support**: Cache questions cho offline mode
3. **Analytics dashboard**: Chi tiết hơn về performance
4. **Gamification**: Thêm achievements, leaderboards
5. **Personalization**: AI recommend câu hỏi phù hợp

### Scalability Considerations
1. **Database optimization**: Index optimization, query caching
2. **CDN integration**: Cache static assets
3. **Microservices**: Tách streak service riêng
4. **Load balancing**: Handle concurrent users
5. **Monitoring**: Performance monitoring và alerting

## 📱 Mobile & Responsive Design

### Current Implementation
- Sử dụng `useScreenSize` hook để detect screen size
- Conditional rendering cho mobile/desktop
- Tailwind CSS responsive classes

### Issues Found
```javascript
// Hardcoded responsive logic
{screenSize?.lte960 ? "text-sm leading-sm" : "text-md leading-md"}
```

### Recommendations
1. **Consistent breakpoints**: Sử dụng Tailwind standard breakpoints
2. **Mobile-first approach**: Design mobile trước, desktop sau
3. **Touch-friendly**: Larger touch targets cho mobile
4. **Performance**: Optimize cho mobile networks

## 🔐 Security Considerations

### Current Security Measures
1. **Authentication**: JWT token validation
2. **User validation**: Check user ownership
3. **Input validation**: Basic client-side validation

### Security Gaps
1. **Rate limiting**: Không có rate limiting cho API calls
2. **CSRF protection**: Thiếu CSRF tokens
3. **Input sanitization**: Thiếu server-side validation
4. **SQL injection**: Raw queries có thể vulnerable

### Security Recommendations
```javascript
// 1. Rate limiting
const rateLimit = {
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100 // limit each IP to 100 requests per windowMs
};

// 2. Input validation
const validateAnswer = (answer) => {
  if (!answer || typeof answer !== 'string') {
    throw new Error('Invalid answer format');
  }
  return answer.trim();
};

// 3. Parameterized queries
const query = `
  SELECT * FROM questions_answers
  WHERE user_id = ? AND streak_question_id = ?
`;
```

## 📈 Performance Metrics

### Current Performance Issues
1. **Large component**: 900+ lines main component
2. **Multiple re-renders**: Excessive useState usage
3. **API waterfall**: Sequential API calls
4. **No caching**: Repeated data fetching

### Performance Optimization Plan
```javascript
// 1. Component splitting
const StreakQuestion = React.memo(({ question, onAnswer }) => {
  // Question component logic
});

// 2. Custom hooks
const useStreakData = (userId) => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Fetch and cache streak data
  }, [userId]);

  return { data, loading };
};

// 3. React Query for caching
const { data: questions, isLoading } = useQuery(
  ['streak-questions', streakId],
  () => getQuestionByStreak(streakId),
  { staleTime: 5 * 60 * 1000 } // 5 minutes
);
```

## 🧪 Testing Strategy

### Current Testing Status
- **Unit tests**: Không có
- **Integration tests**: Không có
- **E2E tests**: Không có

### Testing Recommendations
```javascript
// 1. Unit tests cho business logic
describe('Streak Logic', () => {
  test('should calculate points correctly', () => {
    const result = calculatePoints(
      { point: 10 },
      true,
      true
    );
    expect(result).toBe(20); // x2 for star point
  });
});

// 2. Integration tests cho API
describe('Streak API', () => {
  test('should create streak successfully', async () => {
    const response = await request(app)
      .post('/streaks')
      .send({ user_id: 1, streak_question_id: 1 })
      .expect(201);

    expect(response.body.data).toBeDefined();
  });
});

// 3. E2E tests cho user flow
describe('Streak User Flow', () => {
  test('should complete full streak flow', async () => {
    await page.goto('/quan-ly/streak');
    await page.click('[data-testid="start-streak"]');
    // Test complete flow
  });
});
```

---

*Tài liệu này cung cấp cái nhìn toàn diện về tính năng Streak, giúp Business Analyst hiểu rõ cách thức hoạt động và đưa ra quyết định phát triển phù hợp.*
